import '../../utils/path'; // Import to ensure String.prototype.toPosix is available

import * as vscode from 'vscode';
import cloneDeep from 'clone-deep';
import { JoyCoderSayText } from '../../shared/ExtensionMessage';
import { formatResponse } from '../prompts/responses';
import { JoyCoderRestoreMessageMap } from '../../adaptor/translate/message';
import { defaultModeSlug } from '../../../web-agent/src/utils/modes';
import { JoyCoder, UserContent } from '../Joycoder';
import { validateToolUse } from '../webview/mode-validator';
import { ToolName } from '../../schemas';
import { useReplaceFileTool } from './tools/useReplaceFileTool';
import { applyDiffTool } from './tools/applyDiffTool';
import { fetchInstructionsTool } from './tools/fetchInstructionsTool';
import { insertContentTool } from './tools/insertContentTool';
import { switchModeTool } from './tools/switchModeTool';
import { useWriteFileTool } from './tools/useWriteFile';
import { toolDescription, pushToolResult } from './tools/common';
import { useReadFileTool } from './tools/useReadFileTool';
import { useWebSearchTool } from './tools/useWebSearchTool';
import { useCodeBaseTool } from './tools/useCodebaseTool';
import { newTaskCreationTool } from './tools/newTaskCreationTool';
import { useListFilesTool } from './tools/useListFilesTool';
import { useDefinitionNamesTool } from './tools/useDefinitionNamesTool';
import { useSearchFiles } from './tools/useSearchFiles';
import { useBrowserTool } from './tools/useBrowserTool';
import * as path from 'path';
import { openInVscodeWithBrowser } from '@joycoder/plugin-base-browser';
import { useCommandTool } from './tools/useCommandTool';
import { useMcpTools } from './tools/useMcpTools';
import { getMcpResourceTool } from './tools/getMcpResourceTool';
import { getUserQuestionTool } from './tools/getUserQuestionTool';
import { codenseTool } from './tools/codenseTool';
import { getMcpInstructionsTool } from './tools/getMcpInstructionsTool';
import { attemptTaskDoneTool } from './tools/attemptTaskDoneTool';
import { newTaskWithContextTool } from './tools/newTaskWithContextTool';
import { useClearPublishTool } from './tools/useClearPublishTool';

/**
 * 检测文本内容中是否包含需要自动启动浏览器的关键词
 */
function detectBrowserLaunchKeywords(content: string, cwd: string): { shouldLaunch: boolean; url?: string } {
  const lowerContent = content.toLowerCase().trim();

  // 检测 "start xxx.html" 模式 - 启动HTML文件
  const startHtmlMatch = content.match(/start\s+([^\s]+\.html?)/i);
  if (startHtmlMatch) {
    const fileName = startHtmlMatch[1];
    const filePath = path.isAbsolute(fileName) ? fileName : path.join(cwd, fileName);
    const fileUrl = `file://${filePath}`;
    return { shouldLaunch: true, url: fileUrl };
  }

  // 检测 "open xxx.html" 模式
  const openFileMatch = content.match(/open\s+([^\s]+\.html?)/i);
  if (openFileMatch) {
    const fileName = openFileMatch[1];
    // 构建文件URL - 使用绝对路径
    const filePath = path.isAbsolute(fileName) ? fileName : path.join(cwd, fileName);
    const fileUrl = `file://${filePath}`;
    return { shouldLaunch: true, url: fileUrl };
  }

  // 更宽松的检测 - 检测包含HTML文件名的行
  const htmlFileInLineMatch = content.match(/([a-zA-Z0-9_-]+\.html?)/i);
  if (htmlFileInLineMatch && (lowerContent.includes('open') || lowerContent.includes('start'))) {
    const fileName = htmlFileInLineMatch[1];
    const filePath = path.isAbsolute(fileName) ? fileName : path.join(cwd, fileName);
    const fileUrl = `file://${filePath}`;
    return { shouldLaunch: true, url: fileUrl };
  }

  return { shouldLaunch: false };
}

/**
 * 自动启动浏览器工具
 */
async function autoLaunchBrowser(jc: JoyCoder, url: string, conversationId: string, userContent: UserContent) {
  // 先显示一条消息说明自动启动浏览器
  // await jc.say(
  //   'text',
  //   JSON.stringify({
  //     text: `正在打开内置浏览器: ${url}`,
  //     conversationId: conversationId,
  //     taskId: jc.taskId,
  //     sessionId: jc.sessionId,
  //     userContent: userContent,
  //   } as JoyCoderSayText),
  //   undefined,
  //   false
  // );

  // 直接使用内置浏览器打开，不通过工具调用
  try {
    openInVscodeWithBrowser(url);
  } catch (error) {
    // 如果内置浏览器启动失败，显示错误消息
    await jc.say(
      'text',
      JSON.stringify({
        text: `内置浏览器启动失败: ${error}`,
        conversationId: conversationId,
        taskId: jc.taskId,
        sessionId: jc.sessionId,
        userContent: userContent,
      } as JoyCoderSayText),
      undefined,
      false
    );
  }
}

export async function presentAssistantMessage(
  jc: JoyCoder,
  conversationId: string,
  cwd: string | vscode.Uri,
  userContent: UserContent
) {
  if (jc.abort) {
    throw new Error(JoyCoderRestoreMessageMap['JoyCode instance aborted']);
  }

  if (jc.presentAssistantMessageLocked) {
    jc.presentAssistantMessageHasPendingUpdates = true;
    return;
  }
  jc.presentAssistantMessageLocked = true;
  jc.presentAssistantMessageHasPendingUpdates = false;

  if (jc.currentStreamingContentIndex >= jc.assistantMessageContent.length) {
    // this may happen if the last content block was completed before streaming could finish. if streaming is finished, and we're out of bounds then this means we already presented/executed the last content block and are ready to continue to next request
    if (jc.didCompleteReadingStream) {
      jc.userMessageContentReady = true;
    }
    // console.log("no more content blocks to stream! this shouldn't happen?")
    jc.presentAssistantMessageLocked = false;
    return;
    //throw new Error("No more content blocks to stream! this shouldn't happen...") // remove and just return after testing
  }

  const block = cloneDeep(jc.assistantMessageContent[jc.currentStreamingContentIndex]); // need to create copy bc while stream is updating the array, it could be updating the reference block properties too
  switch (block.type) {
    case 'text': {
      if (jc.didRejectTool || jc.didAlreadyUseTool) {
        break;
      }
      let content = block.content;
      if (content) {
        // Remove partial XML tag at the very end of the content (for tool use and thinking tags)
        // (prevents scrollview from jumping when tags are automatically removed)
        const lastOpenBracketIndex = content.lastIndexOf('<');
        if (lastOpenBracketIndex !== -1) {
          const possibleTag = content.slice(lastOpenBracketIndex);
          // Check if there's a '>' after the last '<' (i.e., if the tag is complete) (complete thinking and tool tags will have been removed by now)
          const hasCloseBracket = possibleTag.includes('>');
          if (!hasCloseBracket) {
            // Extract the potential tag name
            let tagContent: string;
            if (possibleTag.startsWith('</')) {
              tagContent = possibleTag.slice(2).trim();
            } else {
              tagContent = possibleTag.slice(1).trim();
            }
            // Check if tagContent is likely an incomplete tag name (letters and underscores only)
            const isLikelyTagName = /^[a-zA-Z_]+$/.test(tagContent);
            // Preemptively remove < or </ to keep from these artifacts showing up in chat (also handles closing thinking tags)
            const isOpeningOrClosing = possibleTag === '<' || possibleTag === '</';
            // If the tag is incomplete and at the end, remove it from the content
            if (isOpeningOrClosing || isLikelyTagName) {
              content = content.slice(0, lastOpenBracketIndex).trim();
            }
          }
        }
      }

      if (!block.partial) {
        // Some models add code block artifacts (around the tool calls) which show up at the end of text content
        // matches ``` with at least one char after the last backtick, at the end of the string
        const match = content?.trimEnd().match(/```[a-zA-Z0-9_-]+$/);
        if (match) {
          const matchLength = match[0].length;
          content = content.trimEnd().slice(0, -matchLength);
        }
      }
      // 检测是否需要自动启动浏览器（仅在消息完成时检测）
      let shouldInterceptText = false;
      if (!block.partial && content) {
        const cwdString = typeof cwd === 'string' ? cwd : cwd.fsPath;
        const browserLaunch = detectBrowserLaunchKeywords(content, cwdString);
        if (browserLaunch.shouldLaunch && browserLaunch.url) {
          // 检查浏览器工具是否可用
          const { browserSettings } = (await jc.providerRef.deref()?.getState()) || {};
          if (browserSettings) {
            try {
              await autoLaunchBrowser(jc, browserLaunch.url, conversationId, block.userContent || []);
              shouldInterceptText = true; // 标记需要拦截原始文本
            } catch (error) {
              console.error('[Browser Auto Launch] 自动启动浏览器失败:', error);
            }
          }
        }
      }

      // 只有在没有拦截的情况下才显示原始文本
      if (!shouldInterceptText) {
        await jc.say(
          'text',
          JSON.stringify({
            text: content,
            conversationId: jc.conversationId,
            taskId: jc.taskId,
            sessionId: jc.sessionId,
            userContent: block.userContent,
          } as JoyCoderSayText),
          undefined,
          block.partial
        );
      }

      break;
    }
    case 'tool_use':
      if (jc.didRejectTool) {
        // ignore any tool content after user has rejected tool once
        if (!block.partial) {
          jc.userMessageContent.push({
            type: 'text',
            text: `Skipping tool ${await toolDescription(block, jc)} due to user rejecting a previous tool.`,
          });
        } else {
          // partial tool after user rejected a previous tool
          jc.userMessageContent.push({
            type: 'text',
            text: `Tool ${await toolDescription(
              block,
              jc
            )} was interrupted and not executed due to user rejecting a previous tool.`,
          });
        }
        break;
      }

      if (jc.didAlreadyUseTool) {
        // ignore any content after a tool has already been used
        jc.userMessageContent.push({
          type: 'text',
          text: `Tool [${block.name}] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.`,
        });
        break;
      }

      if (block.name !== 'use_browser') {
        await jc.browserSession.closeBrowser();
      }

      // Validate tool use before execution
      const { mode, customModes } = (await jc.providerRef.deref()?.getState()) ?? {};
      try {
        validateToolUse(
          block.name as ToolName,
          mode ?? defaultModeSlug,
          customModes ?? [],
          {
            apply_diff: true,
          },
          block.params
        );
      } catch (error) {
        jc.consecutiveMistakeCount++;
        await pushToolResult(jc, block, formatResponse.toolError(error.message));
        break;
      }

      switch (block.name) {
        case 'apply_diff':
          await applyDiffTool(jc, block);
          break;
        case 'insert_content':
          await insertContentTool(jc, block);
          break;
        case 'fetch_instructions':
          await fetchInstructionsTool(jc, block);
          break;
        case 'switch_mode':
          await switchModeTool(jc, block);
          break;
        case 'use_replace_file':
          await useReplaceFileTool(jc, block);
          break;
        case 'use_write_file':
          await useWriteFileTool(jc, block);
          break;
        case 'use_read_file':
          await useReadFileTool(jc, block);
          break;
        case 'use_web_search':
          await useWebSearchTool(jc, block);
          break;
        case 'use_codebase':
          await useCodeBaseTool(jc, block);
          break;
        case 'use_clear_publish':
          await useClearPublishTool(jc, block);
          break;
        case 'new_task_creation':
          await newTaskCreationTool(jc, block);
          break;
        case 'new_task_with_condense_context':
          await newTaskWithContextTool(jc, block);
          break;
        case 'use_list_files':
          await useListFilesTool(jc, block);
          break;
        case 'use_definition_names':
          await useDefinitionNamesTool(jc, block);
          break;
        case 'use_search_files':
          await useSearchFiles(jc, block);
          break;
        case 'use_browser':
          await useBrowserTool(jc, block);
          break;
        case 'use_command':
          await useCommandTool(jc, block);
          break;
        case 'use_mcp_tools':
          await useMcpTools(jc, block);
          break;
        case 'get_mcp_resource':
          await getMcpResourceTool(jc, block);
          break;
        case 'get_user_question':
          await getUserQuestionTool(jc, block);
          break;
        case 'condense':
          await codenseTool(jc, block);
          break;
        case 'get_mcp_instructions':
          await getMcpInstructionsTool(jc, block);
          break;
        case 'attempt_task_done':
          await attemptTaskDoneTool(jc, block);
          break;
      }
      break;
  }

  const recentlyModifiedFiles = jc.fileContextTracker.getAndClearRecentlyModifiedFiles();

  if (recentlyModifiedFiles.length > 0) {
    // TODO: We can track what file changes were made and only
    // checkpoint those files, this will be save storage.
    // await jc.saveCheckpoint();
  }
  /*
		Seeing out of bounds is fine, it means that the next too call is being built up and ready to add to assistantMessageContent to present.
		When you see the UI inactive during jc, it means that a tool is breaking without presenting any UI. For example the use_write_file tool was breaking when relpath was undefined, and for invalid relpath it never presented UI.
		*/
  jc.presentAssistantMessageLocked = false; // this needs to be placed here, if not then calling jc.presentAssistantMessage below would fail (sometimes) since it's locked
  // NOTE: when tool is rejected, iterator stream is interrupted and it waits for userMessageContentReady to be true. Future calls to present will skip execution since didRejectTool and iterate until contentIndex is set to message length and it sets userMessageContentReady to true itself (instead of preemptively doing it in iterator)
  if (!block.partial || jc.didRejectTool || jc.didAlreadyUseTool) {
    // block is finished streaming and executing
    if (jc.currentStreamingContentIndex === jc.assistantMessageContent.length - 1) {
      // its okay that we increment if !didCompleteReadingStream, it'll just return bc out of bounds and as streaming continues it will call presentAssistantMessage if a new block is ready. if streaming is finished then we set userMessageContentReady to true when out of bounds. this gracefully allows the stream to continue on and all potential content blocks be presented.
      // last block is complete and it is finished executing
      jc.userMessageContentReady = true; // will allow pwaitfor to continue
    }

    // call next block if it exists (if not then read stream will call it when its ready)
    jc.currentStreamingContentIndex++; // need to increment regardless, so when read stream calls this function again it will be streaming the next block

    if (jc.currentStreamingContentIndex < jc.assistantMessageContent.length) {
      // there are already more content blocks to stream, so we'll call this function ourselves
      // await jc.presentAssistantContent()

      presentAssistantMessage(jc, conversationId, cwd, userContent);
      return;
    }
  }
  // block is partial, but the read stream may have finished
  if (jc.presentAssistantMessageHasPendingUpdates) {
    presentAssistantMessage(jc, conversationId, cwd, userContent);
  }
}
