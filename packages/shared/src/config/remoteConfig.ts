/**
 * vscode环境及vscode-webview环境共用
 */
import axios from 'axios';
import { ChatModelConfig } from '@joycoder/plugin-base-ai/src/model';
import { to } from 'await-to-js';
import axiosRetry from 'axios-retry';
import moment from 'moment';
import { reportUmpInner } from '../report/commonReport';
import { getGlobalConfig } from './globalConfig';

axiosRetry(axios, { retries: 3 });

export const MAX_CACHE_MINUTES = 3; // 缓存时间
export interface RemoteConfig {
  chatGPTModelConfigs?: ChatModelConfig[];
  _reqTime?: Date;
}

let _remoteConfig: RemoteConfig = {};
let _workSpaceId = '';

/**
 * 获取最新的远程配置
 * @returns
 */
export async function queryRemoteConfigAsync(workSpaceId: string = _workSpaceId) {
  _workSpaceId = workSpaceId;
  let remoteConfigUrl = 'https://craftx.jd.com/craft/ide_JoyCoderFE/vscode/getConfig';
  const remoteConfigUrlForVscode = 'http://jdhgpt.jd.com/config/getModelList';
  if (process && process.env.PLUGIN_VER === 'business') {
    // http://ppms.jd.com/files/upload
    // 保留文件名
    remoteConfigUrl = 'https://storage.360buyimg.com/jxfe/ppms/u/1167025/joycoder.business.json';
  } else if (process && process.env.PLUGIN_VER === 'ide') {
    remoteConfigUrl = getGlobalConfig().joyCoderBaseUrl + '/api/saas/models/v1/modelList';
  }
  // http://xingyun.jd.com/codingRoot/jx-promote-fe/craftx-project/blob/master/ide_JoyCoderFE/vscode.js
  if (process && process.env.PLUGIN_VER === 'ide') {
    const [thrown, response]: [thrown: any, response: any] = await to(
      axios({
        method: 'post',
        url: remoteConfigUrl,
        headers: {
          'Content-Type': 'application/json',
        },
        data: {},
        timeout: 5 * 60 * 1000,
      })
    );
    if (!response || !response.data) {
      reportUmpInner(21, 1, response);
      throw response;
    }

    const data = response.data;
    data._reqTime = new Date();
    if (process && process.env.PLUGIN_VER === 'ide') {
      const ideDta = data.data;
      // 创建一个新的对象，避免循环引用
      data.chatGPTModelConfigs = JSON.parse(JSON.stringify(ideDta));
    }
    _remoteConfig = data;

    console.log('请求地址---->', remoteConfigUrl);
    console.log('模型列表---->', _remoteConfig);

    reportUmpInner(21, 0, '');
    return _remoteConfig;
  } else {
    const [thrown, response]: [thrown: any, response: any] = await to(
      axios.get(remoteConfigUrl, {
        params: {
          id: workSpaceId,
        },
      })
    );

    // wq.webmonitor.hibox.queryRemoteConfigAsync
    if (thrown) {
      if (thrown.code === 'ENOTFOUND') {
        // HOST问题，兜底使用内网local域名
        remoteConfigUrl = 'http://jx-prmt-craft.jd.local/craft/ide_JoyCoderFE/vscode/getConfig';
        return queryRemoteConfigAsync(workSpaceId);
      }
      reportUmpInner(21, 999, thrown);
      throw thrown;
    }

    if (!response || !response.data) {
      reportUmpInner(21, 1, response);
      throw response;
    }

    const [thrown2, response2]: [thrown: any, response: any] = await to(axios.get(remoteConfigUrlForVscode, {}));

    // wq.webmonitor.hibox.queryRemoteConfigAsync
    if (thrown2) {
      reportUmpInner(21, 999, thrown2);
      throw thrown2;
    }

    if (!response2 || !response2.data) {
      reportUmpInner(21, 1, response2);
      throw response2;
    }

    const data = response.data;
    response.data.chatGPTModelConfigs = response2.data.chatGPTModelConfigs;

    data._reqTime = new Date();
    _remoteConfig = data;

    reportUmpInner(21, 0, '');
    return _remoteConfig;
  }
}

/**
 * 同步获取本地缓存的远程配置，缓存时间3分钟
 * @returns
 */
export function queryRemoteConfigSync() {
  if (!_remoteConfig) {
    queryRemoteConfigAsync();
    return {};
  }

  if (isExpired()) {
    queryRemoteConfigAsync();
  }
  return _remoteConfig;
}

function isExpired() {
  const now = moment(new Date());
  const diff = now.diff(moment(_remoteConfig._reqTime), 'minute');
  if (diff >= MAX_CACHE_MINUTES) return true;
}
