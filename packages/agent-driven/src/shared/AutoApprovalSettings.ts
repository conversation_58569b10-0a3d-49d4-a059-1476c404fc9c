export interface AutoApprovalSettings {
  // Whether auto-approval is enabled
  enabled: boolean;
  // Individual action permissions
  actions: {
    readFiles: boolean; // Read files and directories
    editFiles: boolean; // Edit files
    executeCommands: boolean; // Execute safe commands
    useBrowser: boolean; // Use browser
    useMcp: boolean; // Use MCP servers
  };
  // Global settings
  maxRequests: number; // Maximum number of auto-approved requests
  enableNotifications: boolean; // Show notifications for approval and task completion
  autoExecute: boolean;
}

export const DEFAULT_AUTO_APPROVAL_SETTINGS: AutoApprovalSettings = {
  enabled: true,
  actions: {
    readFiles: true,
    editFiles: false,
    executeCommands: false,
    useBrowser: false, // 默认不启用浏览器工具，需要用户手动勾选
    useMcp: true,
  },
  maxRequests: 100,
  enableNotifications: false,
  autoExecute: false,
};
export const AUTO_APPROVAL_SETTINGS: AutoApprovalSettings = {
  enabled: true,
  actions: {
    readFiles: true,
    editFiles: true,
    executeCommands: true,
    useBrowser: true,
    useMcp: true,
  },
  maxRequests: 100,
  enableNotifications: false,
  autoExecute: false,
};
