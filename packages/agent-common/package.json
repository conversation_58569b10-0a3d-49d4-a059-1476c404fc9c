{"name": "@joycoder/agent-common", "version": "3.1.3", "description": "", "scripts": {"dev:chat": "ts-node src/demo/demoChat.ts", "dev:h5Gen": "ts-node src/demo/demoH5Gen.ts", "dev:watch": "nodemon --watch src -e ts --exec ts-node src/demo/demo.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "engines": {"node": ">=18.20.0"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@langchain/anthropic": "^0.2.2", "@langchain/community": "^0.2.25", "@langchain/core": "^0.2.20", "@langchain/langgraph": "^0.0.26", "@langchain/openai": "^0.2.6", "@langchain/textsplitters": "^0.0.3", "cheerio": "1.0.0-rc.12", "clone-deep": "^4.0.1", "core-js": "^3.37.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "dotenv": "^16.4.5", "globby": "^14.0.2", "html-entities": "^2.4.0", "langchain": "^0.2.3", "mathjs": "^13.0.3", "os-name": "^6.0.0", "p-wait-for": "^5.0.2", "turndown": "^7.2.0", "typescript": "^5.5.3", "uuid": "^9.0.1", "mammoth": "^1.8.0", "web-streams-polyfill": "^4.0.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "pdf-parse": "^1.1.1", "zod": "^3.23.3", "axios": "^1.7.4", "fast-deep-equal": "^3.1.3", "isbinaryfile": "^5.0.2", "openai": "^4.61.0", "serialize-error": "^11.0.3", "strip-ansi": "^7.1.0", "tree-sitter-wasms": "^0.1.11", "web-tree-sitter": "^0.22.6"}, "devDependencies": {"@types/clone-deep": "^4.0.4", "@types/diff": "^5.2.1", "@types/node": "^20.14.14", "@types/pdf-parse": "^1.1.4", "@types/turndown": "^5.0.5", "@vscode/codicons": "^0.0.36", "nodemon": "^3.1.4"}}