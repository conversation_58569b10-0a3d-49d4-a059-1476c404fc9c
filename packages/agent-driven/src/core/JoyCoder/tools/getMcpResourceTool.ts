import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { JoyCoderAskUseMcpServer } from '../../../shared/ExtensionMessage';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function getMcpResourceTool(joyCoder: JoyCoder, block: ToolUse) {
  const server_name: string | undefined = block.params.server_name;
  const uri: string | undefined = block.params.uri;
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        type: 'get_mcp_resource',
        serverName: removeClosingTag(joyCoder, 'server_name', server_name, block.partial),
        uri: removeClosingTag(joyCoder, 'uri', uri, block.partial),
      } as JoyCoderAskUseMcpServer);

      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'use_mcp_server');
        await joyCoder.say('use_mcp_server', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'use_mcp_server');
        await joyCoder.ask('use_mcp_server', partialMessage, block.partial).catch(() => {});
      }

      return;
    } else {
      if (!server_name) {
        joyCoder.consecutiveMistakeCount++;

        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('get_mcp_resource', 'server_name')
        );

        return;
      }
      if (!uri) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('get_mcp_resource', 'uri'));

        return;
      }
      joyCoder.consecutiveMistakeCount = 0;
      const completeMessage = JSON.stringify({
        type: 'get_mcp_resource',
        serverName: server_name,
        uri,
      } as JoyCoderAskUseMcpServer);

      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'use_mcp_server');
        await joyCoder.say('use_mcp_server', completeMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
      } else {
        showNotificationForApprovalIfAutoApprovalEnabled(joyCoder, `JoyCode 想要访问 ${server_name} 上的 ${uri}`);
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'use_mcp_server');
        const didApprove = await askApproval(
          joyCoder,
          block,
          'use_mcp_server',
          completeMessage,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          return;
        }
      }

      // now execute the tool
      await joyCoder.say('mcp_server_request_started');
      const resourceResult = await joyCoder.providerRef.deref()?.mcpHub?.readResource(server_name, uri);
      const resourceResultPretty =
        resourceResult?.contents
          .map((item) => {
            if (item.text) {
              return item.text;
            }
            return '';
          })
          .filter(Boolean)
          .join('\n\n') || '(Empty response)';

      // Handle images (image must contain mimetype and blob)
      let images: string[] = [];

      resourceResult?.contents.forEach((item) => {
        if (item.mimeType?.startsWith('image') && item.blob) {
          if (item.blob.startsWith('data:')) {
            images.push(item.blob);
          } else {
            images.push(`data:${item.mimeType};base64,` + item.blob);
          }
        }
      });
      await joyCoder.say('mcp_server_response', resourceResultPretty, images);
      await pushToolResult(joyCoder, block, formatResponse.toolResult(resourceResultPretty, images));

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'get_mcp_resource', error);

    return;
  }
}
