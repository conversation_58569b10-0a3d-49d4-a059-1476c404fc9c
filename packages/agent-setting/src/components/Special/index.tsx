import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Input, InputNumber, Select, Slider, SliderSingleProps } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox/Checkbox';
import { BROWSER_VIEWPORT_PRESETS } from '@joycoder/agent-driven/src/shared/BrowserSettings';
import { useHandleMessage } from '../../hooks/useHandleMessage';

interface SpecialTabProps {
  completionDelay: number;
  maxConcurrentFileReads: number;
  codeCompletionsMoreContext?: boolean;
  isShowCodeLens: boolean;
  codeLens: string[];
  commitMessage: string;
  browserViewportSize: string;
  isCommitCodeReview: boolean;
  isSilentMode: boolean;
  browserToolEnabled: boolean;
  remoteBrowserEnabled: boolean;
  remoteBrowserHost?: string;
  isErrorLine: boolean;
  // enableBrowserTool: boolean;
  marks: SliderSingleProps['marks'];
  styles: {
    track: { backgroundColor: string };
    tracks: { background: string; border: string };
    rail: { background: string };
  };
  onCompletionDelayChange: (value: number) => void;
  onBrowserToolEnabledChange: (value: CheckboxChangeEvent) => void;
  onRemoteBrowserEnabledChange: (value: CheckboxChangeEvent) => void;
  onCompletionsMoreContextChange?: (e: CheckboxChangeEvent) => void;
  onCodeLensChange: (e: CheckboxChangeEvent) => void;
  onCommitMessageChange: (value: string) => void;
  onBrowserViewportSizeChange: (value: string) => void;
  onRemoteBrowserHostChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onCommitCodeReviewChange: (e: CheckboxChangeEvent) => void;
  onSilentModeChange: (e: CheckboxChangeEvent) => void;
  onErrorLineChange: (e: CheckboxChangeEvent) => void;
  onMaxConcurrentFileReadsChange: (value: number | null) => void;
  // onEnableBrowserToolChange: (e: CheckboxChangeEvent) => void;
}

export default function SpecialTab({
  completionDelay,
  // codeCompletionsMoreContext,
  isShowCodeLens,
  codeLens,
  commitMessage,
  isCommitCodeReview,
  isSilentMode,
  browserToolEnabled,
  isErrorLine,
  // enableBrowserTool,
  marks,
  styles,
  browserViewportSize,
  remoteBrowserEnabled,
  remoteBrowserHost,
  maxConcurrentFileReads,
  onRemoteBrowserEnabledChange,
  onCompletionDelayChange,
  onMaxConcurrentFileReadsChange,
  onCodeLensChange,
  onCommitMessageChange,
  onCommitCodeReviewChange,
  onSilentModeChange,
  onErrorLineChange,
  onBrowserToolEnabledChange,
  onBrowserViewportSizeChange,
  onRemoteBrowserHostChange,
}: SpecialTabProps) {
  const [testingConnection, setTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; text: string } | null>(null);
  const [discovering, setDiscovering] = useState(false);

  // We don't need a local state for useRemoteBrowser since we're using the
  // `enableRemoteBrowser` prop directly. This ensures the checkbox always
  // reflects the current global state.

  useHandleMessage(({ type, data }) => {
    switch (type) {
      case 'browserConnectionResult':
        setTestResult({ success: data.success, text: data.text });
        setTestingConnection(false);
        setDiscovering(false);
        break;
    }
  });

  const testConnection = async () => {
    setTestingConnection(true);
    setTestResult(null);

    try {
      // Send a message to the extension to test the connection.
      vscode.postMessage({
        type: 'COMMON',
        payload: {
          type: 'testBrowserConnection',
        },
        text: remoteBrowserHost,
      });
    } catch (error) {
      setTestResult({
        success: false,
        text: `Error: ${error instanceof Error ? error.message : String(error)}`,
      });
      setTestingConnection(false);
    }
  };

  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">特性</div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title">预测补全</div>
        <div className="joycoder-setting-box-text">生成延迟时间，避免不必要的建议</div>
        <Slider
          marks={marks}
          step={0.1}
          value={completionDelay}
          min={0.3}
          max={3}
          onChange={onCompletionDelayChange}
          trackStyle={styles.track}
          handleStyle={styles.tracks}
          railStyle={styles.rail}
        />
        <div className="joycoder-setting-box-title mt-18">文件并发读取数</div>
        <div className="joycoder-setting-box-text">
          <InputNumber
            placeholder="支持同时处理的最大文件数"
            value={maxConcurrentFileReads}
            onChange={onMaxConcurrentFileReadsChange}
            max={5}
            min={1}
            size="small"
          />
        </div>
        <div className="joycoder-setting-box-tips">
          注：支持同时处理的最大文件数。较高的值可能会加快读取多个小文件的速度，但会增加内存使用量，最大支持5个，最小1个。
        </div>
        {/* <div className="joycoder-setting-box-text">
          <Checkbox
            className="joycoder-setting-box-context"
            checked={codeCompletionsMoreContext}
            onChange={onCompletionsMoreContextChange}
          >
            启用跨文件感知
          </Checkbox>
        </div>
        <div className="joycoder-setting-box-tips">
          注：启用后即可感知当前文件目录及打开的相似文件作为预测补全的上下文
        </div> */}
      </div>
      <div className={!isShowCodeLens ? 'joycoder-setting-box mb-16 hidden' : 'joycoder-setting-box mb-16'}>
        <div className="joycoder-setting-box-title mt-26">行间菜单</div>
        <div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('functionComment')}
              onChange={onCodeLensChange}
              value="functionComment"
            >
              开启生成【函数注释】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('reconstruction')}
              onChange={onCodeLensChange}
              value="reconstruction"
            >
              开启生成【代码重构】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('comment')}
              onChange={onCodeLensChange}
              value="comment"
            >
              开启生成【逐行注释】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('codeReview')}
              onChange={onCodeLensChange}
              value="codeReview"
            >
              开启生成【代码评审】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text mb-16">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('test')}
              onChange={onCodeLensChange}
              value="test"
            >
              开启生成【单元测试】行间展示
            </Checkbox>
          </div>
        </div>
      </div>
      <div className="joycoder-setting-box mb-16">
        <div className="joycoder-setting-box-title">生成Commit Message</div>
        <div className="joycoder-setting-box-text joycoder-flex w-88 mb-16">
          <div>
            <span className="joycoder-setting-box-label w-88">生成模式配置：</span>
          </div>
          <div className="joycolder-setting-box-select-wrap">
            <Select
              className="joycoder-setting-box-select"
              value={commitMessage}
              onChange={onCommitMessageChange}
              placeholder="JoyCoder生成Commit Message配置"
              size="small"
              options={[
                {
                  value: 'GIT_SCHEMA',
                  label: (
                    <>
                      <div>标准模式</div>
                      <div className="joycoder-setting-box-message">根据 Conventional Commits 规范生成；</div>
                      <div className="joycoder-setting-box-message">示例：fix(chat): 接口错误</div>
                    </>
                  ),
                },
                {
                  value: 'BRANCH_SCHEMA',
                  label: (
                    <>
                      <div>分支模式</div>
                      <div className="joycoder-setting-box-message">生成变更摘要 + 分支名称;</div>
                      <div className="joycoder-setting-box-message">示例：fix: 接口错误[分支名称]</div>
                    </>
                  ),
                },
                {
                  value: 'AUTO',
                  label: (
                    <>
                      <div>变更摘要</div>
                      <div className="joycoder-setting-box-message">示例：fix connection error</div>
                    </>
                  ),
                },
              ]}
            />
          </div>
        </div>
      </div>
      <div className="joycoder-setting-box mb-16">
        <div className="joycoder-setting-box-title">代码评审</div>
        <div className="mb-16 v-h mt-10">
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={isCommitCodeReview}
              onChange={onCommitCodeReviewChange}
            >
              开启代码评审增量扫描
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            注：针对每次提交代码进行质量、安全、编码规范、逻辑错误等多维度扫描，并提供修复建议；评审结果在问答窗展示。
          </div>
        </div>
        <div className="mb-16 v-h">
          <div className="joycoder-setting-box-text">
            <Checkbox className="joycoder-setting-box-context" checked={isErrorLine} onChange={onErrorLineChange}>
              开启行间错误提示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            注：指编辑过程中分析存在的质量、安全等问题；直接在编辑区区域红字提示。
          </div>
        </div>
      </div>
      <div className="joycoder-setting-box mb-16">
        <div className="joycoder-setting-box-title">浏览器设置</div>
        <div className="mb-16 v-h mt-10">
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={browserToolEnabled}
              onChange={onBrowserToolEnabledChange}
            >
              启用浏览器工具
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            注：启用后，如果模型支持网络访问功能，JoyCode将能够通过内置浏览器与网站进行交互，帮助您搜索信息、访问网页内容。
          </div>
        </div>
        {browserToolEnabled && (
          <>
            <div className="joycoder-setting-box-text joycoder-flex">
              <div>
                <span className="joycoder-setting-box-label w-88">窗口大小：</span>
              </div>
              <div className="joycolder-setting-box-select-wrap">
                <Select
                  className="joycoder-setting-box-select"
                  value={browserViewportSize}
                  onChange={onBrowserViewportSizeChange}
                  placeholder="JoyCoder浏览器视口大小"
                  size="small"
                  options={
                    Object.values(BROWSER_VIEWPORT_PRESETS).map((preset) => {
                      const { width, height } = preset;
                      return {
                        value: `{"width":${width},"height":${height}}`,
                        label: (
                          <>
                            <div>{`${width}×${height}`}</div>
                          </>
                        ),
                      };
                    }) || []
                  }
                />
              </div>
            </div>
            <div className="joycoder-setting-box-tip">
              注：设置浏览器窗口大小。这决定了看到的浏览器显示区域，会影响网站布局和使用体验。
            </div>
            <div className="mb-16 v-h mt-10">
              <div className="joycoder-setting-box-text">
                <Checkbox
                  className="joycoder-setting-box-context"
                  checked={remoteBrowserEnabled}
                  onChange={onRemoteBrowserEnabledChange}
                >
                  使用远程浏览器链接
                </Checkbox>
              </div>
              <div className="joycoder-setting-box-tip">
                注：连接到已开启远程调试功能的Chrome浏览器（通过启动参数--remote-debugging-port=9222设置）。
              </div>
            </div>
            {remoteBrowserEnabled && (
              <>
                <div className="joycoder-setting-box-text">
                  <div className="joycolder-setting-box-select-wrap pl-24">
                    <Input.Group compact>
                      <Input
                        style={{ width: 'calc(100% - 200px)' }}
                        value={remoteBrowserHost ?? ''}
                        placeholder="请输入自定义URL（如：http://localhost:9222）"
                        onChange={onRemoteBrowserHostChange}
                      />
                      <Button type="primary" onClick={testConnection}>
                        {testingConnection || discovering ? '测试中···' : '测试连接'}
                      </Button>
                    </Input.Group>
                  </div>
                </div>
                {testResult && (
                  <div className={`joycoder-setting-box-tip mt-10 ${testResult.success ? 'color-green' : 'color-red'}`}>
                    {testResult.success ? '连接成功' : '连接失败'}：{testResult.text}
                  </div>
                )}
                <div className="joycoder-setting-box-tip mt-10 mb-16">
                  注：请输入 DevTools 协议主机地址，或留空让系统自动查找本地 Chrome 浏览器。
                  点击"测试连接"按钮将使用您提供的地址进行连接，如果未提供地址，系统将自动查找本地 Chrome。
                </div>
              </>
            )}
          </>
        )}
      </div>
      <div className="joycoder-setting-box mb-16">
        <div className="joycoder-setting-box-title">实验性</div>
        <div className="mb-16 v-h mt-10">
          <div className="joycoder-setting-box-text">
            <Checkbox className="joycoder-setting-box-context" checked={isSilentMode} onChange={onSilentModeChange}>
              后台编辑
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            启用后防止编辑器焦点干扰。文件编辑在后台进行，不会打开差异视图或抢夺焦点。你可以在RoO进行更改时继续不受干扰地工作。文件可以在不获取焦点的情况下打开以捕获诊断信息，或保持完全关闭状态。
          </div>
        </div>
      </div>
    </div>
  );
}
