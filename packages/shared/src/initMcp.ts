import * as vscode from 'vscode';
import { GlobalFileNames } from '@joycoder/agent-driven/src/core/storage/disk';

// MCP configurations list
const joycodeMcpList = {
  'joycode-api': {
    args: ['-y', '@joycode-ide/resources-mcp'],
    env: {
      RESOURCES_API_URL: '',
      RESOURCES_AUTHORIZATION: '',
    },
    command: 'npx',
  },
  'devcloud-deploy': {
    type: 'sse',
    url: 'http://devcloud-mcp-pre.jd.com/devcloud/sse?ptkey=Zmxhc2g9M19td2VlR2gzVENFUEdrVU5MUGVSbDdja2VmRnJBZmZ3T2paSTFxVzQ3MzM3WGFXb3RVX0FPVnVPZWJvWUdrOGdmcmRzazJMYXRFdHczWUtLUUFyVDNueEtWbmpLcnpBQjY3WXh5aW0yQks0V1d2cVBqcTdDX3gwbktZX1k3TXlkTXhtRmphUWxyOExTWWpTNi03akVydFBwakE2byomZWlkPSYzQUI5RDIzRjdBNEIzQ1NTPQ==',
    timeout: 180,
    transportType: 'sse',
  },
};

export async function initMcp() {
  const extension = vscode.extensions.getExtension('publisher.resource');
  console.log('resource插件', extension);
  const initMcpGlobalConfig = async () => {
    try {
      // 检查目录是否存在，不存在则创建
      const globalFolderPath = vscode.Uri.file(
        require('path').join(require('os').homedir(), GlobalFileNames.globalFolder)
      );
      try {
        await vscode.workspace.fs.stat(globalFolderPath);
      } catch (error) {
        // 目录不存在，创建目录
        await vscode.workspace.fs.createDirectory(globalFolderPath);
      }

      const mcpSettingsPath = vscode.Uri.file(
        require('path').join(require('os').homedir(), GlobalFileNames.globalFolder, GlobalFileNames.mcpSettings)
      );

      let mcpConfig: any = {};
      let fileExists = false;

      // 检查文件是否存在并尝试读取
      try {
        const fileData = await vscode.workspace.fs.readFile(mcpSettingsPath);
        const configText = Buffer.from(fileData).toString('utf8');
        mcpConfig = JSON.parse(configText);
        fileExists = true;
      } catch (error) {
        // 文件不存在或解析失败
        fileExists = false;
        vscode.window.showErrorMessage('MCP 全局配置异常，请修复后重新加载窗口', '打开配置文件').then((selection) => {
          if (selection === '打开配置文件') {
            vscode.workspace.openTextDocument(mcpSettingsPath).then((doc) => {
              vscode.window.showTextDocument(doc);
            });
          }
        });
        return;
      }

      // 确保 mcpServers 属性存在
      if (!mcpConfig.mcpServers) {
        mcpConfig.mcpServers = {};
      }

      // 检查是否所有 MCP 配置都已存在
      const allConfigsExist = Object.keys(joycodeMcpList).every((key) => mcpConfig.mcpServers[key]);
      if (allConfigsExist) {
        console.log('所有 MCP 配置已存在，跳过初始化');
        return;
      }

      // 添加 两个mcp 配置
      mcpConfig.mcpServers = {
        ...mcpConfig.mcpServers,
        ...joycodeMcpList,
      };

      // 将更新后的配置写入文件
      const updatedConfigText = JSON.stringify(mcpConfig, null, 2);
      await vscode.workspace.fs.writeFile(mcpSettingsPath, Buffer.from(updatedConfigText, 'utf8'));

      if (fileExists) {
        console.log('joycode-api 和 one-click-deploy MCP 配置已成功添加到现有文件');
      } else {
        console.log('MCP 配置文件已创建，joycode-api 和 one-click-deploy 配置已成功添加');
      }
    } catch (error) {
      console.error('初始化 MCP 配置时发生错误:', error);
    }
  };
  initMcpGlobalConfig();
  if (extension) {
    console.log('resource插件已安装', extension);
    // B插件已安装
    if (extension.isActive) {
      // B插件已激活
      console.log('resource插件已加载完成');
      await initMcpGlobalConfig();
    } else {
      // 等待resource插件激活
      extension.activate().then(async (result) => {
        console.log('resource插件激活完成', result);
        await initMcpGlobalConfig();
      });
    }
  }
}
