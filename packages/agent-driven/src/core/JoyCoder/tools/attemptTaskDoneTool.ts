import Anthropic from '@anthropic-ai/sdk';
import { findLast } from 'lodash-es';
import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { COMPLETION_RESULT_CHANGES_FLAG } from '../../../shared/ExtensionMessage';
import { ToolResponse } from '../../../shared/tools';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  askApproval,
  askFinishSubTaskApproval,
  toolDescription,
  handleError,
} from './common';

export async function attemptTaskDoneTool(joyCoder: JoyCoder, block: ToolUse) {
  const result: string | undefined = block.params.result;
  const command: string | undefined =
    ((await joyCoder.providerRef.deref()?.getState()) || {})?.mode === 'chat' ? undefined : block.params.command;

  const addNewChangesFlagToLastCompletionResultMessage = async () => {
    // Add newchanges flag if there are new changes to the workspace
    try {
      const hasNewChanges = await joyCoder.doesLatestTaskCompletionHaveNewChanges();
      const lastCompletionResultMessage = findLast(joyCoder.JoyCoderMessages, (m) => m.say === 'completion_result');
      if (
        lastCompletionResultMessage &&
        hasNewChanges &&
        !lastCompletionResultMessage.text?.endsWith(COMPLETION_RESULT_CHANGES_FLAG)
      ) {
        lastCompletionResultMessage.text += COMPLETION_RESULT_CHANGES_FLAG;
      }
      await joyCoder.saveJoyCoderMessages();
    } catch (error) {
      console.warn('%c [ error ]-38', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  };

  try {
    const lastMessage = joyCoder.JoyCoderMessages.at(-1);
    if (block.partial) {
      if (command && !joyCoder.parentTask) {
        // the attempt_task_done text is done, now we're getting command
        // remove the previous partial attempt_task_done ask, replace with say, post state to webview, then stream command

        // const secondLastMessage = joyCoder.JoyCoderMessages.at(-2)
        // NOTE: we do not want to auto approve a command run as part of the attempt_task_done tool
        if (lastMessage && lastMessage.ask === 'command') {
          // update command
          await joyCoder
            .ask('command', removeClosingTag(joyCoder, 'command', command, block.partial), block.partial)
            .catch(() => {});
        } else {
          // last message is completion_result
          // we have command string, which means we have the result as well, so finish it (doesnt have to exist yet)
          await joyCoder.say(
            'completion_result',
            removeClosingTag(joyCoder, 'result', result, block.partial),
            undefined,
            false
          );
          await joyCoder.saveCheckpoint(true);
          await addNewChangesFlagToLastCompletionResultMessage();
          await joyCoder
            .ask('command', removeClosingTag(joyCoder, 'command', command, block.partial), block.partial)
            .catch(() => {});
        }
      } else {
        // no command, still outputting partial result
        await joyCoder.say(
          'completion_result',
          removeClosingTag(joyCoder, 'result', result, block.partial),
          undefined,
          block.partial
        );
      }
      return;
    } else {
      if (!result) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('attempt_task_done', 'result')
        );
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;

      let commandResult: ToolResponse | undefined = undefined;
      if (command && !joyCoder.parentTask) {
        if (lastMessage && lastMessage.ask !== 'command') {
          // havent sent a command message yet so first send completion_result then command
          // await joyCoder.say('completion_result', result, undefined, false);
          await joyCoder.say(
            'completion_result',
            JSON.stringify({
              text: result,
              conversationId: joyCoder?.conversationId ?? '',
              taskId: joyCoder?.taskId ?? '',
              sessionId: joyCoder?.sessionId ?? '',
            }),
            undefined,
            false
          );

          await joyCoder.saveCheckpoint(true);
          await addNewChangesFlagToLastCompletionResultMessage();
        } else {
          // we already sent a command message, meaning the complete completion message has also been sent
          await joyCoder.saveCheckpoint(true);
        }

        // complete command message
        const didApprove = await askApproval(
          joyCoder,
          block,
          'command',
          command,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }

        const [userRejected, execCommandResult] = await joyCoder.executeCommandTool(command!);
        if (userRejected) {
          joyCoder.didRejectTool = true;
          await pushToolResult(joyCoder, block, execCommandResult);
          await joyCoder.saveCheckpoint();
          return;
        }
        // user didn't reject, but the command may have output
        commandResult = execCommandResult;
      } else {
        // await joyCoder.say('completion_result', result, undefined, false);

        await joyCoder.say(
          'completion_result',
          JSON.stringify({
            text: result ?? '',
            conversationId: joyCoder?.conversationId ?? '',
            taskId: joyCoder?.taskId ?? '',
            sessionId: joyCoder?.sessionId ?? '',
          }),
          undefined,
          false
        );
        await joyCoder.saveCheckpoint(true);
        await addNewChangesFlagToLastCompletionResultMessage();
      }

      if (joyCoder.parentTask) {
        const didApprove = await askFinishSubTaskApproval(joyCoder, block);
        if (!didApprove) {
          return;
        }
        // tell the provider to remove the current subtask and resume the previous task in the stack
        await joyCoder.providerRef.deref()?.finishSubTask(result);
        return;
      }

      // we already sent completion_result says, an empty string asks relinquishes control over button and field
      const { response, text, images } = await joyCoder.ask('completion_result', '', false, undefined, !!command);
      if (response === 'yesButtonClicked') {
        await pushToolResult(joyCoder, block, ''); // signals to recursive loop to stop (for now this never happens since yesButtonClicked will trigger a new task)
        return;
      }
      joyCoder.newSession();

      if (text || images) {
        await joyCoder.say('user_feedback', text ?? '', images);
      }

      const toolResults: (Anthropic.TextBlockParam | Anthropic.ImageBlockParam)[] = [];
      if (commandResult) {
        if (typeof commandResult === 'string') {
          toolResults.push({
            type: 'text',
            text: commandResult,
          });
        } else if (Array.isArray(commandResult)) {
          toolResults.push(...commandResult);
        }
      }
      toolResults.push({
        type: 'text',
        text: `The user has provided feedback on the results. Consider their input to continue the task, and then attempt completion again.\n<feedback>\n${text}\n</feedback>`,
      });
      toolResults.push(...formatResponse.imageBlocks(images));
      joyCoder.userMessageContent.push({
        type: 'text',
        text: `${await toolDescription(block, joyCoder)} Result:`,
      });
      joyCoder.userMessageContent.push(...toolResults);
      return;
    }
  } catch (error) {
    console.warn('%c [ error ]-205', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    await handleError(joyCoder, block, 'attempt_task_done', error);
    // await joyCoder.saveCheckpoint();
    return;
  }
}
