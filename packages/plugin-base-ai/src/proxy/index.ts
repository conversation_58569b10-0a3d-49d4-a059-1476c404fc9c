import {
  GlobalState,
  hasJdh<PERSON><PERSON><PERSON><PERSON><PERSON>ie,
  getVscodeConfig,
  reportUmp,
  sleep,
  isBusiness,
  forceJdhLogin,
  getJdhLoginInfo,
  getJdhCgiUrl,
  getActionBaseParam,
  Logger,
  isIDE,
  getBaseUrl,
} from '@joycoder/shared';
import to from 'await-to-js';
import { TIMEOUT_MESSAGE, fetchSSE } from './sse';
import { v4 as uuidv4 } from 'uuid';
import { postConfigMessageToWebview, WELCOME_QUESTION } from '../dialog';
import { DEFAULT_SYSTEM_MESSAGE, MessageContent, buildMessageList, setHistoryMessage } from './messages';
import { ChatModel, ChatModelConfig, DEFAULT_CHAT_MODEL, getChatModelAndConfig, setChatModel } from '../model';
import { FileTreeNode } from '../warehouseQuestion';
import { sm2 } from 'sm-crypto';

export enum Scene {
  // 行间聊天场景
  Cursor,
  // 代码
  CodeReview,
  // 聊天窗场景
  Dialog,
  // inlinechat场景
  InlineChat,
  // 预测补全场景，区分配置
  Code,
  // 代码转换
  Translate,
  // 行间菜单
  CodeLens,
}

interface ChatGPTAskParams {
  message: string | MessageContent[];
  model?: string;
  systemMessage?: string;
  chatApiUrl?: string;
  completionsApiUrl?: string;
  apiKey?: string;
  conversationId?: string;
  parentMessageId?: string;
  temperature?: number;
  scene: Scene;
  respMaxTokens?: number;
  maxTotalTokens?: number;
  knowledgeId?: string;
}

export interface ChatGPTAskResponseData {
  id: string;
  role: string;
  parentMessageId: string;
  conversationId: string;
  text: string;
  fullText?: string;
  reasoningContent?: string;
  choices?: {
    text?: string;
    message: {
      reasoning_content?: string;
      content?: string;
    };
    delta: {
      role?: string;
      reasoning_content?: string;
      content?: string;
      content_type?: string;
    };
  }[];
  error?: any; // 调集团网关失败时有该字段
  message?: string; //  错误消息
  messageId?: string; //  当前消息标识
  choicesText?: string; // 仓库问答回答的未处理的文档
  repositoryMessage?: string;
  selectedFileList?: FileTreeNode[]; // 仓库问答引用的文件列表
}

/**
 * 抹平调用各种大模型的差异，处理参数，返回结果
 *
 * @export
 * @class ChatProxyService
 */
export class ChatProxyService {
  public model: string;
  public modelConfig: ChatModelConfig;
  public result: ChatGPTAskResponseData;

  constructor() {
    this.result = {
      id: '',
      role: '',
      parentMessageId: '',
      conversationId: '',
      text: '',
      fullText: '',
      reasoningContent: '',
    };
  }

  /**
   * 20240527-chenlongde
   * 现在的调用逻辑是幂等的，会话和上下文在wenview自己维护。
   * 理论上不需要node端调用reset逻辑
   * 等webview也class化之后再看看是否需要？
   */
  // private resetChatResult() {
  //   this.result.id = '';
  //   this.result.text = '';
  //   this.result.parentMessageId = '';
  //   this.result.conversationId = '';
  //   this.result.fullText = '';
  // }
  // private resetConversation(params) {
  //   this.result.parentMessageId = params.parentMessageId;
  //   this.result.conversationId = params.conversationId;
  // }

  /**
   * 请求chatgpt代理
   *
   * @export
   * @param {ChatGPTAskParams} params
   * @param {*} onProgress
   * @return {*}
   */
  public async invoke(
    params: ChatGPTAskParams,
    options?: {
      abortController?: AbortController;
      onProgress: (response: ChatGPTAskResponseData) => void;
    }
  ) {
    // 检查1次是否有模型额度耗尽的情况
    !isBusiness() && (await this.checkChatGPTModelOverload());

    // 校验登录态
    const isLogin = !!hasJdhLoginCookie();
    const loginGuideText = `未登录，<a id='forceLoginBtn' href="javascript:void(0);">请登录后重试</a>`;

    // 处理”你好“
    if (params.message === WELCOME_QUESTION) {
      if (!isLogin) forceJdhLogin();
      const welcomeAnswer = isLogin ? '您好！有什么可以帮助您的吗？' : loginGuideText;
      this.result.text = welcomeAnswer;
      this.result.fullText = welcomeAnswer;
      return this.result;
    }

    if (!isLogin) {
      const [err, isLogined] = await to(forceJdhLogin());
      // 点击取消授权或登录接口失败时
      if (err || !isLogined) {
        this.result.text = loginGuideText;
        this.result.fullText = loginGuideText;
        this.result.error = 'NOT_LOGIN';
        return this.result;
      }
    }

    // 【模型及参数处理】
    const modelConfig = getChatModelAndConfig(params.model);
    const model = modelConfig.chatApiModel;
    this.model = model;
    this.modelConfig = modelConfig;

    // [chatApiUrl]: 调用接口传的chatApiUrl，实际调用的模型服务地址
    const chatApiUrl =
      params.chatApiUrl || getVscodeConfig('JoyCode.config.chatApiUrl') || modelConfig.chatApiUrl || '';

    // [apiKey]: 调用接口传的apiKey，实际调用的鉴权token
    const apiKey = getVscodeConfig('JoyCode.config.chatToken') || '';

    // [bizId, bizToken]: 走不同的biz通道（需要限制使用GPT4的场景，使用不同的bizId和bizToken）,自定义apiKey的走最普通的joycoderfe（不做限制）
    const bizId = (!apiKey && modelConfig.bizId) || 'joycoderfe';
    const bizToken = (!apiKey && modelConfig.bizToken) || 'd99fa9a5-**************-e0f84dccb099';

    // [maxTotalTokens]: 模型支持的最大token数（问题+回答）
    const maxTotalTokens =
      (!!apiKey && getVscodeConfig('JoyCode.config.chatMaxTotalTokens')) ||
      params.maxTotalTokens ||
      modelConfig.maxTotalTokens ||
      undefined;

    // [systemMessage]: 系统指令，远程配置NONE则不传
    const systemMessage = params.systemMessage || modelConfig.systemMessage || DEFAULT_SYSTEM_MESSAGE;

    // [respMaxTokens]: 应答最大Token数
    const respMaxTokens = params.respMaxTokens || modelConfig.respMaxTokens || 2500;

    // [isNotConversation]: 是否是对话场景
    const isCursor = params.scene == Scene.Cursor;
    const isCode = params.scene == Scene.Code;
    const isInlineChat = params.scene == Scene.InlineChat;
    const isNotConversation =
      isCursor || isInlineChat || isCode || GlobalState.get('chatCurrentIsConversation') === false;

    const parentMessageId = params.parentMessageId || this.result.parentMessageId || '';
    let conversationId = params.conversationId || this.result.conversationId || '';
    if (!conversationId || isNotConversation) conversationId = uuidv4();

    const baseParam = getActionBaseParam();
    const jdhLoginInfo = getJdhLoginInfo();
    const messages = buildMessageList({
      message: params.message,
      systemMessage,
      conversationId,
      parentMessageId,
      respMaxTokens,
      maxTotalTokens,
      modelConfig,
    });

    this.result.text = '';
    this.result.fullText = '';

    // https://joyspace.jd.com/pages/x5Z1ozZIzmjXMZWwh3v0
    // let apiUrl = 'http://jdhgpt.jd.com/v1/chat/completions';
    // let apiUrl = 'http://chatgpt-relay.jd.com/v1/chat/completions';
    let apiUrl = 'http://jdhgpt.jd.com/open/v1/chat/completions';
    let jsonBody: Record<string, number | string | boolean | object> = {
      // 流式输出
      stream: true,
      // 实际调用的模型名
      model,
      // 问题+回答+最新的问题
      messages,
      // 0~2的浮点数，数值越大，结果越多样性
      temperature:
        params.temperature !== undefined
          ? params.temperature
          : this.modelConfig?.temperature !== undefined
          ? this.modelConfig?.temperature
          : 0.1,
      // 用户设置的应答最大Token数，默认值是2500
      max_tokens: respMaxTokens,
    };

    if (isBusiness()) {
      apiUrl = getJdhCgiUrl('http://jdhgpt.jd.com/es/bigdata/sendGeneralStream');
      // 商业化版本参数，走JDH代理
      jsonBody = {
        ...jsonBody,
        chatSessionId: uuidv4(),
        timestamp: Date.now(),
        pluginVersion: baseParam.pluginVersion,
        ideaVersion: baseParam.ideVersion,
        osName: baseParam.osName,
        mac: baseParam.mac,
        userName: jdhLoginInfo?.userName || '',
        userToken: jdhLoginInfo?.userToken || '',
        // 商业化版本传入open
        sourceType: 'open',
      };
    } else if (modelConfig.features?.includes('client') && chatApiUrl) {
      // 内部部署，不兼容请求header里ContentLength的奇怪版本，走本地客户端
      apiUrl = chatApiUrl;
      jsonBody = { ...jsonBody };
    } else if (isIDE()) {
      // IDE版本
      apiUrl = getJdhCgiUrl(getBaseUrl() + '/api/saas/openai/v1/chat/completions');
      jsonBody = { ...jsonBody };

      const encryptData = sm2.doEncrypt(JSON.stringify(jsonBody), jdhLoginInfo?.pk ?? '', 1);
      // Logger.log('Chat接口请求参数：', encryptData);
      jsonBody = {
        ...jsonBody,
        enData: encryptData,
      };
    } else {
      // 内部版本参数，走零售代理，追加extParams
      jsonBody = {
        ...jsonBody,
        ...getJdhLoginInfo(),
        sourceType: 'joyCoderFe',
        // extParams: {
        //   bizId,
        //   bizToken,
        //   ssoClientId: 'SSO1.0',
        //   apiUrl: chatApiUrl,
        //   apiKey,
        //   jdhLoginParams: {
        //     ...getJdhLoginInfo(),
        //     sourceType: 'joyCoderFe',
        //   },
        // },
      };
    }

    // Logger.log('Chat接口请求参数：', JSON.stringify(jsonBody));
    return new Promise<ChatGPTAskResponseData>((resolve, reject) => {
      const baseHeaders = {
        'Content-Type': 'application/json; charset=UTF-8',
      };

      const headers =
        isIDE() && jdhLoginInfo?.ptKey
          ? {
              ...baseHeaders,
              ptKey: jdhLoginInfo.ptKey,
            }
          : baseHeaders;
      fetchSSE(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(jsonBody),
        signal: options?.abortController?.signal,
        onMessage: async (resStr: string) => {
          try {
            // 返回超时
            if (resStr == TIMEOUT_MESSAGE) {
              reportUmp(26, 9999, this.result.fullText);
              this.result.fullText = '系统繁忙，请新开会话重试，或稍后再试~';
              return resolve(this.result);
            }

            // 返回流式结束
            if (resStr == '[DONE]') {
              this.result.text = this.result.text.trim();
              this.result.fullText = this.result.fullText?.trim();
              this.result.reasoningContent = this.result.reasoningContent?.trim();
              Logger.log('Chat接口返回的原始完整内容：', this.result.fullText);
              // wq.webmonitor.hibox.chatGPT.API
              reportUmp(26, 0);
              setHistoryMessage({
                conversationId,
                message: {
                  role: 'assistant',
                  content: this.result.fullText || '',
                  extendMsg: {
                    id: this.result.id || uuidv4(),
                    conversationId,
                  },
                },
                modelConfig,
              });
              return resolve(this.result);
            }

            // 返回数据命中敏感词
            if (resStr == '[INVALID_RESP]') {
              reportUmp(26, 8888, this.result.fullText);
              this.result.fullText = '返回数据中包含敏感信息~';
              return resolve(this.result);
            }

            let code: string | number = 0;
            // eslint-disable-next-line init-declarations
            const data: ChatGPTAskResponseData = JSON.parse(resStr);
            let message = '';

            // 调集错误的情况
            if (data?.error) {
              code = data.error.code;
              // 模型额度耗尽
              if (code == 8) {
                await this.handleChatGPTModelOverload(model);
                if (isCursor) {
                  resolve(this.invoke(params, options));
                } else {
                  resolve({
                    ...this.result,
                    fullText: `您今天的 ${modelConfig.label} 额度已耗尽，已自动切换至 ${DEFAULT_CHAT_MODEL} 模型，明天首次使用时将会自动切换回 ${modelConfig.label} 模型。本次问答可通过问题右上角重试按钮发起重试~`,
                  });
                }
                return;
              }

              if (code == 'LOGIN_CHECK' || code == 13) {
                message = loginGuideText;
                forceJdhLogin();
              } else {
                message = `网关错误信息：${JSON.stringify(data.error)}`;
                reportUmp(26, code, resStr);
              }

              if (params.scene === Scene.CodeLens) {
                this.result.message = message;
                return reject(this.result);
              }
              if (params.scene === Scene.Code || params.scene === Scene.Cursor) return reject(this.result);
              this.result.text = message;
              this.result.fullText = message;
              options?.onProgress?.(this.result);
              return resolve(this.result);
            }

            // 返回数据中
            const text = data?.choices?.[0]?.delta?.content || data?.choices?.[0]?.text || '';
            const reasoning_content = data?.choices?.[0]?.delta?.reasoning_content || ''; // 深度思考
            const contentType = data?.choices?.[0]?.delta?.content_type || 'text';
            if (text || reasoning_content) {
              this.result.id = data.id;
              this.result.text = text;
              this.result.messageId = data.id || uuidv4();
              this.result.conversationId = conversationId;
              this.result.fullText = this.result.fullText + text;
              this.result.reasoningContent = this.result.reasoningContent + reasoning_content;
              // 星火等文生图返回base64字符串时转为md格式
              if (contentType == 'image' && !this.result.fullText.startsWith('![')) {
                this.result.fullText = `![x.png](data:image/png;base64,${this.result.fullText})`;
              }
              options?.onProgress?.(this.result);
            }
          } catch (err) {
            Logger.error('Chat stream SEE event unexpected error', err);
            return reject(err);
          }
        },
      }).catch((error: Error) => {
        // 只要不是主动中止的异常，则需要抛出异常
        // 参考：https://github.com/node-fetch/node-fetch/blob/main/docs/ERROR-HANDLING.md
        if (error.name !== 'AbortError') {
          Logger.error('request error', error);
          reportUmp(26, 9999, error);
          return reject(error);
        }
      });
    });
  }

  async handleChatGPTModelOverload(model = ChatModel.GPT4 as string) {
    const now = new Date();
    now.setDate(now.getDate() + 1);
    now.setHours(0, 0, 0, 0);
    const tomorrowTimestamp = now.getTime();
    // 记录时间戳到明天0点，如果明天0点之前，有新的配置，就重置时间戳
    GlobalState.update('chatGPTModelOverload', {
      model,
      timeStamp: tomorrowTimestamp,
    });
    // 如果有模型额度耗尽的情况，就切换到备用模型
    setChatModel(DEFAULT_CHAT_MODEL);
    postConfigMessageToWebview({ model: DEFAULT_CHAT_MODEL });
    // 等待200ms，避免webview更新不及时
    await sleep(200);
  }

  async checkChatGPTModelOverload() {
    const chatGPTModelOverload = GlobalState.get('chatGPTModelOverload');
    if (chatGPTModelOverload && Date.now() >= chatGPTModelOverload.timeStamp) {
      // 如果有模型额度耗尽切换备用模型的情况，就切换回耗尽额度的模型
      setChatModel(chatGPTModelOverload.model);
      postConfigMessageToWebview({ model: chatGPTModelOverload.model });
      GlobalState.update('chatGPTModelOverload', null);
      // 等待200ms，避免webview更新不及时
      await sleep(200);
    }
  }
}
