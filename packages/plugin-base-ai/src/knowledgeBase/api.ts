import to from 'await-to-js';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import os from 'os';

interface DataSetsProps {
  page?: number;
  page_size?: number;
  name?: string;
  orderby?: string;
  desc?: string;
  dataset_id?: string;
  id?: string;
}
interface DocInfo extends DataSetsProps {
  keywords?: string;
  document_id?: string;
  document_name?: string;
}

export class DataSetsAPI {
  private static readonly BASE_URL = 'http://agentflow.jd.com/api/v1/datasets';
  private static readonly baseHeaders = {
    Authorization: 'Bearer ragflow-I1OTI2MmMwNjYyYzExZjBiOGFlMDI0Mm',
  };
  private static readonly headers = {
    ...this.baseHeaders,
    'content-Type': 'application/json',
  };

  public static async getDataSets(props?: DataSetsProps) {
    const { page, page_size, name, orderby, desc } = props || {};
    const [err, response]: [any, any] = await to(
      axios.get(`${this.BASE_URL}`, {
        headers: this.baseHeaders,
        params: {
          page,
          page_size,
          name,
          orderby,
          desc,
        },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data?.data || [];
    try {
      const dataStesPath = path.join(os.homedir(), '.joycode', 'knowledge-base.json');
      fs.writeFileSync(dataStesPath, JSON.stringify(resData));
    } catch (error) {
      console.error('%c [ error ]-54', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    return resData;
  }

  public static async addDataSets(props?: DataSetsProps) {
    const { name } = props || {};
    const [err, response]: [any, any] = await to(
      axios.post(
        `${this.BASE_URL}`,
        {
          name,
        },
        { headers: this.headers }
      )
    );
    if (err) {
      return null;
    }
    const resData = response.data;
    return resData;
  }

  public static async updateDataSetsById(props?: DataSetsProps) {
    const { name, dataset_id, id } = props || {};
    const [err, response]: [any, any] = await to(
      axios.put(
        `${this.BASE_URL}/${dataset_id || id}`,
        {
          name,
        },
        {
          headers: this.headers,
        }
      )
    );
    if (err) {
      return [];
    }
    const resData = response.data;
    return resData;
  }

  public static async delDataSetsById(props?: DataSetsProps) {
    const { dataset_id, id } = props || {};
    const [err, response]: [any, any] = await to(
      axios.delete(`${this.BASE_URL}`, {
        headers: this.headers,
        data: { ids: [dataset_id || id] },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data;
    return resData;
  }
  public static async getDocList(props?: DocInfo) {
    const { page, page_size, orderby, desc, keywords, dataset_id, document_id, document_name } = props || {};
    const [err, response]: [any, any] = await to(
      axios.get(`${this.BASE_URL}/${dataset_id}/documents`, {
        headers: this.baseHeaders,
        params: {
          page,
          page_size,
          name: document_name,
          orderby,
          desc,
          keywords,
          id: document_id,
        },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data?.data || [];
    return resData;
  }
}
