{"name": "@joycoder/shared", "version": "3.1.3", "description": "", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git", "private": true, "license": "MIT", "main": "src/index.ts", "module": "src/index.ts", "files": ["src"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git"}, "sideEffects": false, "scripts": {}, "bugs": {"url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@joycoder/web": "workspace:*", "ignore": "^7.0.3", "@test/joycoder-rag-mcp-server": "^1.0.77", "uuid": "^9.0.1", "zod": "^3.23.8"}}