/**
 * 同步原插件配置到新插件并卸载原插件
 */
import * as vscode from 'vscode';
import to from 'await-to-js';
import { serializeError } from 'serialize-error';
import { getVscodeConfig, setVscodeConfig, GlobalState, reportUmp, Logger, PLUGIN_ID } from '@joycoder/shared';

export default async function syncConfigAndUninstallOld() {
  // 新插件如果使用的是老空间，则将其强制置为新空间
  const joycoder = vscode.extensions.getExtension(PLUGIN_ID);
  if (joycoder) {
    const workspaceId = getVscodeConfig('JoyCode.config.workSpaceId');
    if (workspaceId == 'hibox') {
      await setVscodeConfig('JoyCode.config.workSpaceId', 'joycoderfe');
    }
  }

  // 有老插件才继续
  const old = vscode.extensions.getExtension('hibox-team.hibox');
  if (!old) return;

  // 未同步过配置才继续
  const stateKey = 'JoyCode-IsSyncedConfig';
  if (GlobalState.get(stateKey)) return;

  const [, result] = await to(
    Logger.showInformationMessage(
      '【HiBox更名公告】尊敬的用户，为了更好地在集团内统一建设编码辅助工具，HiBox将更名为JoyCoder（功能和体验未变）。在新的品牌下，我们将在IDE及AI领域持续探索，继续为您提供高效、专业的编码辅助服务，感谢您一直以来的支持和理解！',
      '同步原插件配置并使用新插件',
      '暂时保留两者，稍后自行同步卸载'
    )
  );
  if (result == '同步原插件配置并使用新插件') {
    try {
      // 同步配置
      const originConfig = getVscodeConfig(undefined, undefined, 'HiBox');
      for (const key of Object.keys(originConfig)) {
        if (key == 'snippets') {
          await setVscodeConfig(`JoyCode.config.${key}.switch`, originConfig[key].switch);
        } else if (key == 'chatgptKnowledge') {
          await setVscodeConfig(`JoyCode.config.${key}.options`, originConfig[key].options);
        } else {
          // hibox中有但joycoder中没有的配置不同步，否则报错，如HiBox.config.chatgptToken
          const isExistConfig = !!getVscodeConfig(`JoyCode.config.${key}`, undefined);
          if (isExistConfig) {
            await setVscodeConfig(`JoyCode.config.${key}`, originConfig[key]);
          }
        }
      }
      // 同步成功标识
      GlobalState.update(stateKey, true);
      reportUmp(39, 0);
    } catch (error) {
      console.error(error);
      // wq.webmonitor.joycoderfe.syncConfigAndUninstallOld
      reportUmp(39, 1, error);
      Logger.showErrorMessage(
        `同步原插件配置失败，请加群 [1026086734](http://joycoder.jd.com?timeline=1) 截图反馈~ 错误信息：${JSON.stringify(
          serializeError(error)
        )}`
      );
      return;
    }
    // 卸载老插件
    await vscode.commands.executeCommand('workbench.extensions.uninstallExtension', 'hibox-team.hibox');
    // 重载以更新
    vscode.commands.executeCommand('workbench.action.reloadWindow');
  }
}
