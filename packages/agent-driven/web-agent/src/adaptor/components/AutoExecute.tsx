import { useEffect, useState } from "react";
import { Switch } from 'antd';
import { vscode } from "../../utils/vscode";
import {
    AutoApprovalSettings,
    DEFAULT_AUTO_APPROVAL_SETTINGS,
} from '@joycoder/agent-driven/src/shared/AutoApprovalSettings';
import { useExtensionState } from "../../context/ExtensionStateContext";


//  自动执行组件
const AutoExecuteTool = () => {
    const { autoApprovalSettings: globalAutoApprovalSettings } = useExtensionState();
       const [autoApprovalSettings, setAutoApprovalSettings] =
        useState<AutoApprovalSettings>(globalAutoApprovalSettings || DEFAULT_AUTO_APPROVAL_SETTINGS);

    const [autoExecute, setAutoExecute] = useState(() => {
        return globalAutoApprovalSettings?.autoExecute || false;
    });


    useEffect(() => {
        const cloneAutoApprovalSettings = JSON.parse(JSON.stringify(autoApprovalSettings));
        cloneAutoApprovalSettings.autoExecute = autoExecute;
        setAutoApprovalSettings(cloneAutoApprovalSettings);

        try {
            vscode.postMessage({
                type: 'autoApprovalSettings', // 直接使用正确的类型
                autoApprovalSettings: cloneAutoApprovalSettings,
            });
        } catch (error) {
            console.error(error);
        }

    }, [autoExecute])


    return (
        <>
            <div className="joycoder-toolbar-switch-group">
                <Switch
                    size="small"
                    checkedChildren="Auto"
                    unCheckedChildren="Auto"
                    checked={autoExecute}
                    onChange={setAutoExecute}
                />
            </div>
        </>
    )
}

export default AutoExecuteTool;
