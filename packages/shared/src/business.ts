import { getVscodeConfig } from './config';
import { GlobalState } from './globalState';
import * as vscode from 'vscode';

//是否是商业化
export function isBusiness() {
  return process && process.env.PLUGIN_VER == 'business';
}
export function isIDE() {
  return process && process.env.PLUGIN_VER == 'ide';
}

export function getBaseUrl() {
  // 检查 GlobalState 是否已初始化
  if (!GlobalState.context) {
    console.warn('GlobalState not initialized yet, returning default URL');
    return 'http://joycoder-api-inner.jd.com';
  }

  if (isIDE()) {
    const baseUrl = vscode.workspace.getConfiguration().get('server.baseUrl');
    return baseUrl !== undefined && baseUrl !== ''
      ? baseUrl
      : GlobalState?.get('jdhLoginInfo')?.masterBaseUrl ?? 'http://joycoder-api-inner.jd.com';
  }

  return GlobalState?.get('jdhLoginInfo')?.masterBaseUrl ?? 'http://joycoder-api-inner.jd.com';
}

//是否是商业化的本地部署（无方访问外部网络）
export function isBusinessLocal() {
  return process && process.env.BUSINESS_ENV == 'local';
}

export function getJdhCgiUrl(url: string) {
  if (isBusiness()) {
    const serverUrl = getVscodeConfig('JoyCode.config.server.url');
    //商业化登录地址
    // return url.replace('http://jdhgpt.jd.com/', 'http://10.207.111.46/joycoder/'); // 内部测试
    // return url.replace('http://jdhgpt.jd.com/', 'http://172.19.241.133/joycoder/'); // 宁德时代
    // return url.replace('http://jdhgpt.jd.com/', 'http://10.1.249.120/joycoder/'); // 浙江大华
    return url.replace('http://jdhgpt.jd.com/', serverUrl); //云服务版本
  }
  return url;
}
