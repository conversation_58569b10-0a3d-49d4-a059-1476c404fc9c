import React, { useEffect, useState } from 'react';
import { Space } from 'antd';
import Table, { ColumnsType, TablePaginationConfig } from 'antd/lib/table';
import { FilterValue, SorterResult } from 'antd/lib/table/interface';

interface DocProps {
  docList: DocInfo[];
}
interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

interface DocInfo {
  name: string;
  id: string;
  dataset_id?: string;
  knowledgebase_id?: string;
  permission?: string;
  update_date?: string;
  tenant_id?: string;
  status?: string;
  description?: string;
  update_time: number;
  create_time?: number;
  token_num?: number;
  source_type?: string;
  progress?: number;
  progress_msg?: string;
  type?: string;
  run?: string;
}

export default function DocListPopup({ docList }: DocProps) {
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date?.toLocaleString('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
    },
  });
  // const [data, setData] = useState();
  const reChunks = (record: DocInfo) => {
    console.log('%c [ record ]-41', 'font-size:13px; background:pink; color:#bf2c9f;', record);
  };
  const delDoc = (record: DocInfo) => {
    console.log('%c [ record ]-41', 'font-size:13px; background:pink; color:#bf2c9f;', record);
  };
  const columns: ColumnsType<DocInfo> = [
    {
      title: '文档名称',
      dataIndex: 'name',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      sorter: {
        compare: (a, b) => a.update_time - b.update_time,
      },
      render: (_, record) => {
        return formatDate(record.update_time);
      },
    },
    {
      title: '解析状态',
      dataIndex: 'status',
      render: (_, record) => {
        return record.status === '2' ? '已解析' : '未解析';
      },
    },
    {
      title: '启用状态',
      dataIndex: 'run',
      render: (_, record) => {
        return record.run === '2' ? '已启动' : '未启动';
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => reChunks(record)}>重新学习</a>
          <a onClick={() => delDoc(record)}>删除</a>
        </Space>
      ),
    },
  ];
  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<DocInfo> | SorterResult<DocInfo>[]
  ) => {
    // 处理单个排序和多个排序的情况
    let sortField: string | undefined = undefined;
    let sortOrder: string | undefined = undefined;

    if (Array.isArray(sorter)) {
      sortField = sorter[0]?.field ? String(sorter[0].field) : undefined;
      sortOrder = sorter[0]?.order ? String(sorter[0].order) : undefined;
    } else {
      sortField = sorter.field ? String(sorter.field) : undefined;
      sortOrder = sorter.order ? String(sorter.order) : undefined;
    }

    setTableParams({
      pagination,
      filters,
      sortField,
      sortOrder,
    });
  };
  return (
    <Table
      rowKey={(record) => record?.id}
      columns={columns}
      dataSource={docList}
      pagination={tableParams.pagination}
      onChange={handleTableChange}
    />
  );
}
