import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import delay from 'delay';
import { everyLineHasLineNumbers, stripLineNumbers, addLineNumbers } from '../../../integrations/misc/extract-text';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { fileExistsAtPath } from '../../../utils/fs';
import { getReadablePath } from '../../../utils/path';
import { unescapeHtmlEntities } from '../../../utils/text-normalization';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { askApproval, handleError, pushToolResult, removeClosingTag } from './common';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { reportAction, ActionType, WorkspaceState, Logger } from '@joycoder/shared';

export async function applyDiffTool(joyCoder: JoyCoder, block: ToolUse) {
  const relPath: string | undefined = block.params.path;
  let diffContent: string | undefined = block.params.diff;

  if (diffContent && !joyCoder.api.getModel().id.includes('claude')) {
    diffContent = unescapeHtmlEntities(diffContent);
  }

  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'appliedDiff',
    path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relPath, block.partial)),
    diff: diffContent,
  };

  try {
    if (block.partial) {
      // Update GUI message
      let toolProgressStatus;

      if (joyCoder.diffStrategy && joyCoder.diffStrategy.getProgressStatus) {
        toolProgressStatus = joyCoder.diffStrategy.getProgressStatus(block);
      }

      if (toolProgressStatus && Object.keys(toolProgressStatus).length === 0) {
        return;
      }

      const partialMessage = JSON.stringify(sharedMessageProps);
      await joyCoder.ask('tool', partialMessage, block.partial, toolProgressStatus).catch(() => {});
      return;
    } else {
      if (!relPath) {
        joyCoder.consecutiveMistakeCount++;
        // joyCoder.recordToolError('apply_diff');
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('apply_diff', 'path', 'tool')
        );
        // await joyCoder.saveCheckpoint();
        return;
      }

      if (!diffContent) {
        joyCoder.consecutiveMistakeCount++;
        // joyCoder.recordToolError('apply_diff');
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('apply_diff', 'diff', 'tool')
        );
        // await joyCoder.saveCheckpoint();
        return;
      }

      const accessAllowed = joyCoder.JoyCoderIgnoreController?.validateAccess(relPath);

      if (!accessAllowed) {
        // await joyCoder.say('joycoderignore_error', relPath);
        const formattedError = formatResponse.toolError(formatResponse.joycoderIgnoreError(relPath));
        Logger.error('apply_diff joycoderingnore_error', formattedError);
        await pushToolResult(joyCoder, block, formattedError);
        // await joyCoder.saveCheckpoint();
        return;
      }

      const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relPath);
      const fileExists = await fileExistsAtPath(absolutePath);

      if (!fileExists) {
        joyCoder.consecutiveMistakeCount++;
        // joyCoder.recordToolError('apply_diff');
        const displayPath = FileSystemHelper.getRemotePath(absolutePath);
        const formattedError = formatResponse.toolError(formatResponse.fileNotExistError(displayPath));
        // await joyCoder.say('error', formattedError);
        Logger.error(formattedError);
        await pushToolResult(joyCoder, block, formattedError);
        // await joyCoder.saveCheckpoint();
        return;
      }

      const originalContent = await FileSystemHelper.readFile(absolutePath, 'utf-8');

      // Apply the diff to the original content
      const diffResult = (await joyCoder.diffStrategy?.applyDiff(
        originalContent,
        diffContent,
        parseInt(block.params.start_line ?? '')
      )) ?? {
        success: false,
        error: 'No diff strategy available',
      };

      if (!diffResult.success) {
        joyCoder.consecutiveMistakeCount++;
        const currentCount = (joyCoder.consecutiveMistakeCountForApplyDiff.get(relPath) || 0) + 1;
        joyCoder.consecutiveMistakeCountForApplyDiff.set(relPath, currentCount);
        let formattedError = '';

        if (diffResult.failParts && diffResult.failParts.length > 0) {
          for (const failPart of diffResult.failParts) {
            if (failPart.success) {
              continue;
            }

            const errorDetails = failPart.details ? JSON.stringify(failPart.details, null, 2) : '';

            formattedError = `<error_details>\n${failPart.error}${
              errorDetails ? `\n\nDetails:\n${errorDetails}` : ''
            }\n</error_details>`;
          }
        } else {
          const errorDetails = diffResult.details ? JSON.stringify(diffResult.details, null, 2) : '';

          formattedError = `Unable to apply diff to file: ${absolutePath}\n\n<error_details>\n${diffResult.error}${
            errorDetails ? `\n\nDetails:\n${errorDetails}` : ''
          }\n</error_details>`;
        }

        // if (currentCount >= 4) {
        //   await joyCoder.say('diff_error', formattedError);
        // }
        console.log('apply_diff', formattedError);
        Logger.error('apply_diff' + formattedError);
        // joyCoder.recordToolError('apply_diff', formattedError);

        await pushToolResult(joyCoder, block, formattedError);
        await joyCoder.saveCheckpoint();
        return;
      }

      joyCoder.consecutiveMistakeCount = 0;
      joyCoder.consecutiveMistakeCountForApplyDiff.delete(relPath);

      // Show diff view before asking for approval
      joyCoder.diffViewProvider.editType = 'modify';
      await joyCoder.diffViewProvider.open(relPath);
      const newContent = everyLineHasLineNumbers(diffResult.content)
        ? stripLineNumbers(diffResult.content)
        : diffResult.content;

      //数据上报1
      // AI生成代码内容上报
      try {
        reportAction({
          actionCate: 'ai',
          actionType: ActionType.AutoGenerator,
          question: block.userContent?.map((item: any) => item?.text)?.join('\n'),
          result: diffContent,
          conversationId: joyCoder.conversationId,
          model: WorkspaceState.get('openAiModelId'),
          startTime: new Date(),
          extendMsg: {
            type: 'yesButtonClicked',
            modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
            taskId: joyCoder.taskId,
            sessionId: joyCoder.sessionId,
          },
        });
        // AI生成代码采纳打标
        AdoptResultCache.setRemote(
          newContent,
          WorkspaceState.get('openAiModelId'),
          AdoptResultCache.ADOPT_CODE_SOURCE.AUTO_CODE,
          joyCoder.conversationId
        );
      } catch (error) {
        console.error('%c [ reportAction->error ]-241', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }

      await joyCoder.diffViewProvider.update(newContent, true);
      await delay(300); // wait for diff view to update
      await joyCoder.diffViewProvider.scrollToFirstDiff();

      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        diff: diffContent,
      } satisfies JoyCoderSayTool);

      let toolProgressStatus = undefined;

      if (joyCoder.diffStrategy && joyCoder.diffStrategy.getProgressStatus) {
        toolProgressStatus = joyCoder.diffStrategy.getProgressStatus(block, diffResult);
      }
      if (!joyCoder.shouldAutoApproveTool(block.name)) {
        const didApprove = await askApproval(joyCoder, block, 'tool', completeMessage);
        // const didApprove = await joyCoder
        //   .ask('tool', completeMessage, false, toolProgressStatus)
        //   .then((response) => response.response === 'yesButtonClicked');
        if (!didApprove) {
          await joyCoder.diffViewProvider.revertChanges(); // joyCoder likely handles closing the diff view
          await joyCoder.saveCheckpoint();
          return;
        }
      } else {
        try {
          reportAction({
            actionCate: 'ai',
            accept: 1,
            actionType: ActionType.copy,
            question: block.userContent?.map((item: any) => item?.text)?.join('\n'),
            result: '',
            conversationId: joyCoder.conversationId,
            model: WorkspaceState.get('openAiModelId'),
            startTime: new Date(),
            extendMsg: {
              type: 'yesButtonClicked',
              modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
              taskId: joyCoder.taskId,
              sessionId: joyCoder.sessionId,
            },
          });
        } catch (error) {
          console.error('%c [ reportAction->error ]-241', 'font-size:13px; background:pink; color:#bf2c9f;', error);
        }
      }
      AdoptResultCache.clearAutoCodeAdopt();

      const { newProblemsMessage, userEdits, finalContent } = await joyCoder.diffViewProvider.saveChanges();

      // Track file edit operation
      if (relPath) {
        await joyCoder.fileContextTracker.trackFileContext(relPath, 'JoyCoder_edited');
      }

      // Used to determine if we should wait for busy terminal to update before sending api request
      joyCoder.didEditFile = true;
      let partFailHint = '';

      if (diffResult.failParts && diffResult.failParts.length > 0) {
        partFailHint = `But unable to apply all diff parts to file: ${FileSystemHelper.getRemotePath(
          absolutePath
        )}, silently use <use_read_file> tool to check newest file version and re-apply diffs\n`;
      }

      if (userEdits) {
        await joyCoder.say(
          'user_feedback_diff',
          JSON.stringify({
            tool: fileExists ? 'editedExistingFile' : 'newFileCreated',
            path: getReadablePath(joyCoder.cwd, relPath),
            diff: userEdits,
          } satisfies JoyCoderSayTool)
        );

        await pushToolResult(
          joyCoder,
          block,
          `The user made the following updates to your content:\n\n${userEdits}\n\n` +
            partFailHint +
            `The updated content, which includes both your original modifications and the user's edits, has been successfully saved to ${FileSystemHelper.getRemotePath(
              relPath
            ).toPosix()}. Here is the full, updated content of the file, including line numbers:\n\n` +
            `<final_file_content path="${FileSystemHelper.getRemotePath(relPath).toPosix()}">\n${addLineNumbers(
              finalContent || ''
            )}\n</final_file_content>\n\n` +
            `Please note:\n` +
            `1. You do not need to re-write the file with these changes, as they have already been applied.\n` +
            `2. Proceed with the task using this updated file content as the new baseline.\n` +
            `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.` +
            `${newProblemsMessage}`
        );
      } else {
        await pushToolResult(
          joyCoder,
          block,
          `Changes successfully applied to ${FileSystemHelper.getRemotePath(
            relPath
          ).toPosix()}:\n\n${newProblemsMessage}\n` + partFailHint
        );
      }

      await joyCoder.diffViewProvider.reset();
      await joyCoder.saveCheckpoint();
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'apply_diff', error);
    await joyCoder.diffViewProvider.reset();
    // await joyCoder.saveCheckpoint();

    return;
  }
}
