{"name": "joycoder-business", "displayName": "JoyCode", "description": "专注于AI和IDE的研发工具集，为您提供高效、智能、专业的编码辅助服务", "publisher": "JoyCode", "version": "3.1.3", "private": true, "engines": {"vscode": "^1.74.0", "node": ">=16.14.0", "pnpm": ">=8"}, "categories": ["Other"], "keywords": ["chatgpt", "openai", "copilot", "<PERSON><PERSON>", "jd", "JoyCode", "ai", "autocomplete", "inline completion"], "icon": "assets/logo.png", "activationEvents": ["*"], "main": "./dist/extension.js", "sideEffects": false, "scripts": {"watch:web": "pnpm -C ../../ run watch:web", "watch:extension": "webpack --watch --mode development", "watch": "rimraf ./dist && npm-run-all -p watch:web watch:extension", "build:web": "pnpm -C ../../ run build:web", "build": "webpack --mode production", "package": "pnpm build && pnpm build:web && vsce package --allow-star-activation --no-dependencies --allow-missing-repository", "publish": "vsce publish --allow-star-activation --no-dependencies --allow-missing-repository"}, "contributes": {"icons": {"completion-icon": {"description": "completion icon", "default": {"fontPath": "assets/iconfont/iconfont.woff", "fontCharacter": "\\e800"}}, "completion-disabled-icon": {"description": "completion disabled icon", "default": {"fontPath": "assets/iconfont/disabled-iconfont.woff", "fontCharacter": "\\e800"}}}, "inlineCompletions": [{"language": "*", "disallowedPrecedingCharacters": [".", ":", ";"], "provider": {"resolveProvider": true}}], "viewsContainers": {"activitybar": [{"id": "JoyCode", "title": "JoyCode", "icon": "assets/activitybar.svg"}]}, "views": {"JoyCode": [{"type": "webview", "id": "JoyCode-left-view", "name": "", "when": "true"}]}, "viewsWelcome": [{"view": "JoyCode-left-view", "contents": ""}], "commands": [{"command": "JoyCode.code-completions-setting", "title": "JoyCode: 预测补全设置", "icon": {"light": "assets/light/completion.svg", "dark": "assets/dark/completion.svg"}}, {"command": "JoyCode.config.setting", "title": "JoyCode: 设置", "icon": {"light": "assets/light/setting.svg", "dark": "assets/dark/setting.svg"}}, {"command": "JoyCode.code-completions-init", "title": "补全代码出现时回调"}, {"command": "JoyCode.openHomePage", "title": "JoyCode: 官方文档", "shortTitle": "官方文档"}, {"command": "JoyCode.openConfigPage", "title": "插件配置", "shortTitle": "插件设置"}, {"command": "JoyCode.openGlobalKeybindings", "title": "快捷键设置", "shortTitle": "快捷键设置"}, {"command": "JoyCode.ClearGlobalState", "title": "清除缓存", "shortTitle": "清除缓存"}, {"command": "JoyCode.downloadHistory", "title": "导出历史会话", "shortTitle": "导出历史会话"}, {"command": "JoyCode.fixbug.clearDiffHighlights", "title": "fixbug: 清除diff高亮"}, {"command": "JoyCode.fixbug.acceptChange", "title": "fixbug: 采纳"}, {"command": "JoyCode.fixbug.rejectChange", "title": "fixbug: 拒绝"}, {"command": "JoyCode.LogOut", "title": "退出登录"}, {"command": "JoyCode.ai.inlineChat.enter", "title": "提交"}, {"command": "JoyCode.ai.inlineChat.delete", "title": "关闭"}, {"command": "JoyCode.ai.inlineChat.abort", "title": "中断问答"}, {"command": "JoyCode.ai.terminal.explain", "title": "JoyCode: 解释选中内容"}, {"command": "JoyCode.codelens.optimization", "title": "代码优化"}, {"command": "JoyCode.codelens.functionComment", "title": "函数注释"}, {"command": "JoyCode.codelens.comment", "title": "逐行注释"}, {"command": "JoyCode.codelens.reconstruction", "title": "代码重构"}, {"title": "解释代码", "command": "JoyCode.codelens.explain"}, {"title": "单元测试", "command": "JoyCode.codelens.test"}, {"command": "JoyCode.ai.chat.explain", "title": "解释代码"}, {"command": "JoyCode.ai.chat.broken", "title": "报错分析"}, {"command": "JoyCode.ai.chat.refactor", "title": "优化代码"}, {"command": "JoyCode.ai.chat.test", "title": "生成单测"}, {"command": "JoyCode.ai.chat.doc", "title": "文档生成"}, {"command": "JoyCode.ai.chat.review", "title": "代码评审"}, {"command": "JoyCode.ai.code.review", "title": "JoyCode代码评审", "icon": {"light": "assets/code-review.svg", "dark": "assets/code-review.svg"}}, {"command": "JoyCode.ai.chat.autoFill", "title": "代码自动填充"}, {"command": "JoyCode.code-completions", "title": "预测补全"}, {"command": "JoyCode.completion.formatAccept", "title": "代码预测采纳执行事件"}, {"command": "JoyCode.completion.get-new-completion", "title": "代码预测自动执行事件"}, {"command": "JoyCode.code-completion.manually", "title": "代码预测主动触发"}, {"command": "JoyCode.codelens.optimization", "title": "代码优化"}, {"command": "JoyCode.codelens.functionComment", "title": "函数注释"}, {"command": "JoyCode.codelens.comment", "title": "逐行注释"}, {"command": "JoyCode.codelens.reconstruction", "title": "代码重构"}, {"title": "解释代码", "command": "JoyCode.codelens.explain"}, {"title": "单元测试", "command": "JoyCode.codelens.test"}, {"command": "JoyCode.ai.chat.transformCodeJS", "title": "转为JavaScript"}, {"command": "JoyCode.ai.chat.transformCodeTS", "title": "转为TypeScript"}, {"command": "JoyCode.ai.chat.optimizedHTML", "title": "HTML语义优化"}, {"command": "JoyCode.ai.chat.transformCodeVue", "title": "转为Vue2.x"}, {"command": "JoyCode.ai.chat.transformCodeVue2TS", "title": "转为Vue2.x-TS"}, {"command": "JoyCode.ai.chat.transformCodeVue3", "title": "转为Vue3.x"}, {"command": "JoyCode.ai.chat.transformCodeVue3TS", "title": "转为Vue3.x-TS"}, {"command": "JoyCode.ai.chat.transformCodeReact", "title": "转为React"}, {"command": "JoyCode.ai.chat.transformCodeReactTS", "title": "转为React-TS"}, {"command": "JoyCode.ai.chat.transformCodeTaro", "title": "转为Taro"}, {"command": "JoyCode.ai.chat.transformCodeTaroVueTS", "title": "转为Taro-Vue-TS"}, {"command": "JoyCode.ai.chat.transformCodeTaroReactTS", "title": "转为Taro-React-TS"}, {"command": "JoyCode.ai.chat.transformCodeHarmonyArkTS", "title": "转为鸿蒙-ArkTS"}], "menus": {"terminal/context": [{"command": "JoyCode.ai.terminal.explain", "group": "navigation@01"}], "comments/commentThread/context": [{"command": "JoyCode.ai.inlineChat.enter", "group": "inline", "when": "commentController == joycoder-inline-chat"}], "comments/commentThread/title": [{"command": "JoyCode.ai.inlineChat.abort", "group": "navigation@1", "when": "commentController == joycoder-inline-chat && when.JoyCode.inlineChat.streaming"}, {"command": "JoyCode.ai.inlineChat.delete", "group": "navigation@2", "when": "commentController == joycoder-inline-chat"}], "view/title": [{"command": "JoyCode.code-completions-setting", "when": "view == JoyCode-left-view", "group": "navigation@1"}, {"command": "JoyCode.config.setting", "when": "view == JoyCode-left-view", "group": "navigation@2"}, {"command": "JoyCode.openConfigPage", "when": "view == JoyCode-left-view", "group": "overflow2@1"}, {"command": "JoyCode.openGlobalKeybindings", "when": "view == JoyCode-left-view", "group": "overflow2@2"}, {"command": "JoyCode.ClearGlobalState", "when": "view == JoyCode-left-view", "group": "overflow2@3"}, {"command": "JoyCode.LogOut", "when": "view == JoyCode-left-view", "group": "overflow2@4"}, {"command": "JoyCode.downloadHistory", "when": "view == JoyCode-left-view", "group": "overflow2@5"}, {"command": "JoyCode.openHomePage", "when": "view == JoyCode-left-view", "group": "overflow3@1"}], "scm/title": [{"command": "JoyCode.ai.code.review", "group": "navigation@6"}], "editor/title": [], "explorer/context": [], "editor/context": [{"submenu": "JoyCode.ai.chat.context", "group": "AJoyCoder@1"}, {"submenu": "JoyCode.ai.chat.transformCode", "group": "AJoyCoder@2"}], "JoyCode.ai.chat.context": [{"command": "JoyCode.ai.chat.explain", "group": "AJoyCoder@1"}, {"command": "JoyCode.ai.chat.broken", "group": "AJoyCoder@2"}, {"command": "JoyCode.ai.chat.refactor", "group": "AJoyCoder@3"}, {"command": "JoyCode.ai.chat.test", "group": "AJoyCoder@4"}, {"command": "JoyCode.ai.chat.doc", "group": "AJoyCoder@5"}, {"command": "JoyCode.ai.chat.review", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@6"}, {"command": "JoyCode.ai.chat.autoFill", "group": "BJoyCoder@1", "when": "editorHasSelection"}], "JoyCode.ai.chat.transformCode": [{"command": "JoyCode.ai.chat.transformCodeVue", "group": "AJoyCoder@1"}, {"command": "JoyCode.ai.chat.transformCodeVue2TS", "group": "AJoyCoder@2"}, {"command": "JoyCode.ai.chat.transformCodeVue3", "group": "AJoyCoder@3"}, {"command": "JoyCode.ai.chat.transformCodeVue3TS", "group": "AJoyCoder@4"}, {"command": "JoyCode.ai.chat.transformCodeReact", "group": "AJoyCoder@5"}, {"command": "JoyCode.ai.chat.transformCodeReactTS", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@6"}, {"command": "JoyCode.ai.chat.transformCodeTaro", "group": "AJoyCoder@7"}, {"command": "JoyCode.ai.chat.transformCodeTaroVueTS", "group": "AJoyCoder@8"}, {"command": "JoyCode.ai.chat.transformCodeTaroReactTS", "group": "<PERSON>oyCoder@9"}, {"command": "JoyCode.ai.chat.transformCodeHarmonyArkTS", "group": "<PERSON><PERSON>Coder@10"}, {"command": "JoyCode.ai.chat.transformCodeTS", "group": "<PERSON><PERSON>Coder@11"}, {"command": "JoyCode.ai.chat.transformCodeJS", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@12"}, {"command": "JoyCode.ai.chat.optimizedHTML", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON>@13"}]}, "submenus": [{"id": "JoyCode.ai.chat.context", "group": "AJoyCoder", "label": "JoyCode: AI助手"}, {"id": "JoyCode.ai.chat.transformCode", "group": "AJoyCoder", "label": "JoyCode: 代码翻译"}], "configuration": {"type": "object", "title": "JoyCode", "properties": {"JoyCode.config.server.url": {"type": "string", "default": "http://joycoder-man.jd-eit.com/coder/", "markdownDescription": "JoyCoder服务端HTTP地址(IP)"}, "JoyCode.config.chatModel": {"type": "string", "default": "JoyCode-Lite", "markdownDescription": "调用JoyCoder的大模型版本"}, "JoyCode.config.codeCompletionsMaxTokens": {"type": "number", "default": 1000, "markdownDescription": "预测补全续写最大Tokens"}, "JoyCode.config.codeCompletionsTemperature": {"type": "number", "default": 0, "markdownDescription": "预测补全续写模型温度0-1之间"}, "JoyCoder.config.codeCompletionsMaxLines": {"type": "number", "default": 5, "markdownDescription": "预测补全续写最大行数"}, "JoyCoder.config.completionsRejectTimes": {"type": "number", "default": 20, "markdownDescription": "连续不采纳次数超过设置次数关闭预测功能"}, "JoyCoder.config.codeCompletionsMaxTimes": {"type": "number", "default": 4000, "markdownDescription": "预测补全续写超时时间，默认4000毫秒"}, "JoyCoder.config.codeCompletionsMoreContext": {"type": "boolean", "markdownDescription": "启用跨文件感知，识别同目录及打开的相似文件", "default": false}, "JoyCoder.config.codeCompletionsFormat": {"type": "boolean", "markdownDescription": "启用代码补全格式化", "default": false}, "JoyCoder.config.codeCompletionsGenTask": {"type": "string", "default": "LINE", "enum": ["LINE", "BLOCK", "FUNCTION", "TIME_OUT"], "enumItemLabels": ["整行优先", "代码块模式", "函数优先", "速度优先"], "enumDescriptions": ["按行生成代码，效果好，适用广，速度快", "优先生成代码块，速度稍慢", "生成函数级代码，速度较慢", "根据超时时间生成代码，速度取决于设置超时时间"], "markdownDescription": "设置JoyCoder代码预测生成场景"}, "JoyCoder.config.codeReview.commitMessageStyle": {"type": "string", "default": "GIT_SCHEMA", "enum": ["GIT_SCHEMA", "BRANCH_SCHEMA", "AUTO", "DIY"], "enumItemLabels": ["标准模式", "分支模式", "变更摘要"], "enumDescriptions": ["根据 Conventional Commits 规范生成；\n示例：fix(chat): 接口错误", "生成变更摘要 + 分支名称;\n示例：fix: 接口错误[分支名称]", "生成变更摘要；\n示例：fix connection error"], "markdownDescription": "JoyCoder生成Commit Message配置"}, "JoyCoder.config.codeLens-row-menus": {"type": "boolean", "markdownDescription": "是否启用启用行间菜单", "default": true}, "JoyCode.config.chatModels.list": {"type": "array", "default": [], "markdownDescription": "AI助手相关模型配置", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "定义ChatGPT的模型名称"}, "description": {"type": "string", "description": "定义ChatGPT的模型描述"}, "chatApiModel": {"type": "string", "description": "定义ChatGPT的API模型"}, "maxTotalTokens": {"type": "number", "description": "定义ChatGPT的模型最大总tokens"}}}}}}, "keybindings": [{"command": "JoyCode.ai.inlineChat.create", "key": "shift+alt+k", "mac": "shift+alt+k", "when": "editorTextFocus"}, {"command": "JoyCode.ai.inlineChat.create", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus"}, {"command": "JoyCode.ai.inlineChat.delete", "key": "escape", "when": "when.JoyCode.RunEsc"}, {"command": "editor.action.inlineSuggest.commit", "key": "Enter", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.commit", "key": "Tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.hide", "key": "Esc", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.showNext", "key": "shift+alt+down", "mac": "shift+alt+down", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.showPrevious", "key": "shift+alt+up", "mac": "shift+alt+up", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.acceptNextLine", "mac": "shift+tab", "key": "shift+tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.acceptNextWord", "mac": "alt+.", "key": "alt+.", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.trigger", "key": "shift+alt+.", "mac": "shift+alt+.", "when": "editorTextFocus"}, {"command": "JoyCode.code-completion.manually", "key": "shift+alt+right", "mac": "shift+alt+right", "when": "editorTextFocus"}], "snippets": []}, "dependencies": {"@joycoder/plugin-base-ai": "workspace:*", "@joycoder/plugin-base-code-completion": "workspace:*", "@joycoder/plugin-base-code-review": "workspace:*", "@joycoder/shared": "workspace:*", "@joycoder/version": "workspace:*", "gpt-tokens": "^1.3.3"}}