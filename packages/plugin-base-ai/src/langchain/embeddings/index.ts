import { type ClientOptions, OpenAI as OpenAIClient } from 'openai';
import { OpenAIEmbeddings, OpenAIEmbeddingsParams } from '@langchain/openai';
import { LegacyOpenAIInput } from '@langchain/openai';
import { forceJdhLogin, hasJdh<PERSON>oginCookie } from '@joycoder/shared';
import to from 'await-to-js';
import { getExtParams, IExtParams } from '../shared/utils';
import { BASE_URL } from '../shared/constants';

/**
 *使用JoyCoder私有服务的Embedding类
 * @link https://js.langchain.com/docs/integrations/text_embedding/openai
 * @example
 * ```typescript
 *const model = new JoyCoderAIEmbeddings();
 *const res = await model.embedQuery('hello');
 *Logger.log('JoyCoderAIEmbeddings', { res });
 * ```
 */
// @ts-ignore
export class JoyCoderAIEmbeddings extends OpenAIEmbeddings {
  constructor(
    fields?: Partial<OpenAIEmbeddingsParams> & {
      verbose?: boolean;
      openAIApiKey?: string;
      configuration?: ClientOptions;
    },
    configuration?: ClientOptions & LegacyOpenAIInput
  ) {
    const newFields = fields ? { ...fields } : {};
    const newConfiguration = configuration ? { ...configuration } : {};

    // 绕过OpenAIEmbeddings内部校验
    newFields.openAIApiKey = 'not-empty';

    // 设置服务URL
    newFields.configuration = {
      ...newFields.configuration,
      baseURL: BASE_URL,
    };

    // 禁用默认的重试次数
    newFields.maxRetries = newFields.maxRetries ?? 0;

    super(newFields, newConfiguration);
  }

  async embeddingWithRetry(request: OpenAIClient.EmbeddingCreateParams & { extParams: IExtParams }) {
    // 校验登录态，引导登录，更新登录参数
    const isLogin = !!hasJdhLoginCookie();
    if (!isLogin) {
      await to(forceJdhLogin());
    }
    request.extParams = getExtParams();
    // @ts-ignore
    return await super.embeddingWithRetry(request);
  }
}
