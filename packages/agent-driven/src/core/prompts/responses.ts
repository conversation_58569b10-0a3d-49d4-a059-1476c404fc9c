import { Anthropic } from '@anthropic-ai/sdk';
import * as vscode from 'vscode';
import * as diff from 'diff';
import { JoyCoderIgnoreController, LOCK_TEXT_SYMBOL } from '../ignore/JoycoderIgnoreController';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

export const formatResponse = {
  duplicateFileReadNotice: () =>
    `[[NOTE] This file read has been removed to save space in the context window. Refer to the latest file read for the most up to date version of this file.]`,
  toolApprovedWithFeedback: (feedback?: string) =>
    `The user approved this operation and provided the following context:\n<feedback>\n${feedback}\n</feedback>`,

  contextTruncationNotice: () =>
    `[NOTE] Some previous conversation history with the user has been removed to maintain optimal context window length. The initial user task and the most recent exchanges have been retained for continuity, while intermediate conversation history has been removed. Please keep this in mind as you continue assisting the user.`,
  condense: () =>
    `The user has accepted the condensed conversation summary you generated. This summary covers important details of the historical conversation with the user which has been truncated.\n<explicit_instructions type="condense_response">It's crucial that you respond by ONLY asking the user what you should work on next. You should NOT take any initiative or make any assumptions about continuing with work. For example you should NOT suggest file changes or attempt to read any files.\nWhen asking the user what you should work on next, you can reference information in the summary which was just generated. However, you should NOT reference information outside of what's contained in the summary for this response. Keep this response CONCISE.</explicit_instructions>`,

  toolDenied: () => `The user denied this operation.`,

  toolDeniedWithFeedback: (feedback?: string) =>
    `The user denied this operation and provided the following feedback:\n<feedback>\n${feedback}\n</feedback>`,

  toolError: (error?: string) =>
    `The tool execution failed with the following error:\n<error>\n${error}\n</error> Your next response should ONLY reply tool use XML-tags, DONOT apologize or reply any other words.`,

  joycoderIgnoreError: (path: string) =>
    `Access to ${path} is blocked by the .joycodeignore file settings. You must try to continue in the task without using this file, or ask the user to update the .joycodeignore file.`,

  noToolsUsed: () =>
    `[ERROR] You did not use a tool in your previous response! Please retry with a tool use. Your next response should ONLY reply tool use XML-tags, DONOT apologize or reply any other words.

${toolUseInstructionsReminder}

# Next Steps

If you have completed the user's task, use the attempt_task_done tool.
If you require additional information from the user, use the get_user_question tool.
Otherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task.
(This is an automated message, so do not respond to it conversationally.)`,

  tooManyMistakes: (feedback?: string) =>
    `You seem to be having trouble proceeding. The user has provided the following feedback to help guide you:\n<feedback>\n${feedback}\n</feedback>`,

  missingToolParameterError: (paramName: string) =>
    `Missing value for required parameter '${paramName}'. Please retry with complete response. \n\n${toolUseInstructionsReminder}`,

  invalidMcpToolArgumentError: (serverName: string, toolName: string) =>
    `Invalid JSON argument used with ${serverName} for ${toolName}. Please retry with a properly formatted JSON argument.`,

  fileNotExistError: (displayPath: string) =>
    `File does not exist at path: ${displayPath}\n\n<error_details>\nThe specified file could not be found. Please verify the file path and try again. Your next response should ONLY reply tool use XML-tags, DONOT apologize or reply any other words.\n</error_details>`,

  toolResult: (
    text: string,
    images?: string[]
  ): string | Array<Anthropic.TextBlockParam | Anthropic.ImageBlockParam> => {
    if (images && images.length > 0) {
      const textBlock: Anthropic.TextBlockParam = { type: 'text', text };
      const imageBlocks: Anthropic.ImageBlockParam[] = formatImagesIntoBlocks(images);
      // Placing images after text leads to better results
      return [textBlock, ...imageBlocks];
    } else {
      return text;
    }
  },
  lineCountTruncationError: (actualLineCount: number, isNewFile: boolean, diffStrategyEnabled: boolean = false) => {
    const truncationMessage = `Note: Your response may have been truncated because it exceeded your output limit. You wrote ${actualLineCount} lines of content, but the line_count parameter was either missing or not included in your response.`;

    const newFileGuidance =
      `This appears to be a new file.\n` +
      `${truncationMessage}\n\n` +
      `RECOMMENDED APPROACH:\n` +
      `1. Try again with the line_count parameter in your response if you forgot to include it\n` +
      `2. Or break your content into smaller chunks - first use use_write_file with the initial chunk\n` +
      `3. Then use insert_content to append additional chunks\n`;

    let existingFileApproaches = [
      `1. Try again with the line_count parameter in your response if you forgot to include it`,
    ];

    if (diffStrategyEnabled) {
      existingFileApproaches.push(`2. Or try using apply_diff instead of use_write_file for targeted changes`);
    }

    existingFileApproaches.push(
      `${diffStrategyEnabled ? '3' : '2'}. Or use use_replace_file for specific text replacements`,
      `${diffStrategyEnabled ? '4' : '3'}. Or use insert_content to add specific content at particular lines`
    );

    const existingFileGuidance =
      `This appears to be content for an existing file.\n` +
      `${truncationMessage}\n\n` +
      `RECOMMENDED APPROACH:\n` +
      `${existingFileApproaches.join('\n')}\n`;

    return `${isNewFile ? newFileGuidance : existingFileGuidance}\n${toolUseInstructionsReminder}`;
  },

  imageBlocks: (images?: string[]): Anthropic.ImageBlockParam[] => {
    return formatImagesIntoBlocks(images);
  },

  formatFilesList: (
    absolutePath: string | vscode.Uri,
    files: string[],
    didHitLimit: boolean,
    joycoderIgnoreController?: JoyCoderIgnoreController
  ): string => {
    const sorted = files
      .map((file) => {
        // convert absolute path to relative path
        const relativePath = FileSystemHelper.relative(absolutePath, file).toPosix();
        return file.endsWith('/') ? relativePath + '/' : relativePath;
      })
      // Sort so files are listed under their respective directories to make it clear what files are children of what directories. Since we build file list top down, even if file list is truncated it will show directories that joycode can then explore further.
      .sort((a, b) => {
        const aParts = a.split('/'); // only works if we use toPosix first
        const bParts = b.split('/');
        for (let i = 0; i < Math.min(aParts.length, bParts.length); i++) {
          if (aParts[i] !== bParts[i]) {
            // If one is a directory and the other isn't at this level, sort the directory first
            if (i + 1 === aParts.length && i + 1 < bParts.length) {
              return -1;
            }
            if (i + 1 === bParts.length && i + 1 < aParts.length) {
              return 1;
            }
            // Otherwise, sort alphabetically
            return aParts[i].localeCompare(bParts[i], undefined, {
              numeric: true,
              sensitivity: 'base',
            });
          }
        }
        // If all parts are the same up to the length of the shorter path,
        // the shorter one comes first
        return aParts.length - bParts.length;
      });

    const joycoderIgnoreParsed = joycoderIgnoreController
      ? sorted.map((filePath) => {
          // path is relative to absolute path, not cwd
          // validateAccess expects either path relative to cwd or absolute path
          // otherwise, for validating against ignore patterns like "assets/icons", we would end up with just "icons", which would result in the path not being ignored.
          const absoluteFilePath = FileSystemHelper.resolve(absolutePath, filePath);
          const isIgnored = !joycoderIgnoreController.validateAccess(absoluteFilePath);
          if (isIgnored) {
            return LOCK_TEXT_SYMBOL + ' ' + filePath;
          }

          return filePath;
        })
      : sorted;

    if (didHitLimit) {
      return `${joycoderIgnoreParsed.join(
        '\n'
      )}\n\n(File list truncated. Use use_list_files on specific subdirectories if you need to explore further.)`;
    } else if (
      joycoderIgnoreParsed.length === 0 ||
      (joycoderIgnoreParsed.length === 1 && joycoderIgnoreParsed[0] === '')
    ) {
      return 'No files found.';
    } else {
      return joycoderIgnoreParsed.join('\n');
    }
  },

  createPrettyPatch: (filename = 'file', oldStr?: string, newStr?: string) => {
    // strings cannot be undefined or diff throws exception
    const patch = diff.createPatch(filename.toPosix(), oldStr || '', newStr || '');
    const lines = patch.split('\n');
    const prettyPatchLines = lines.slice(4);
    return prettyPatchLines.join('\n');
  },
};

// to avoid circular dependency
const formatImagesIntoBlocks = (images?: string[]): Anthropic.ImageBlockParam[] => {
  return images
    ? images.map((dataUrl) => {
        // data:image/png;base64,base64string
        const [rest, base64] = dataUrl.split(',');
        const mimeType = rest.split(':')[1].split(';')[0];
        return {
          type: 'image',
          source: {
            type: 'base64',
            media_type: mimeType,
            data: base64,
          },
        } as Anthropic.ImageBlockParam;
      })
    : [];
};

const toolUseInstructionsReminder = `# Reminder: Instructions for Tool Use

Tool uses are formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>

For example:

<attempt_task_done>
<result>
I have completed the task...
</result>
</attempt_task_done>

Always adhere to this format for all tool uses to ensure proper parsing and execution.`;
