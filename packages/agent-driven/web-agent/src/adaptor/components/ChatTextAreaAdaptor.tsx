import React, { forwardRef, useMemo, useState, useCallback, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON>, Switch } from 'antd';
import DynamicTextArea from 'react-textarea-autosize';
import ContextMenu from '../../components/chat/ContextMenu';
import Thumbnails from '../../components/common/Thumbnails';
import CustomDropdown, { DropdownMenuItem } from '../../components/common/CustomDropdown';
import CustomTooltip from '../../components/common/CustomTooltip';
import { FileImageOutlined, ApiOutlined, LikeOutlined } from '@ant-design/icons';
import { ChatModelConfig, DEFAULT_ASSISTANT_AVATAR } from '../locales/types';
import { useEvent } from 'react-use';
import './index.css';
import './index.scss';
import 'antd/lib/tooltip/style/index.css';
import 'antd/lib/switch/style/index.css';
import 'antd/lib/button/style/index.css';
import { vscode } from '../../utils/vscode';
import { ExtensionMessage, JoyCoderApiReqInfo } from '../../../../src/shared/ExtensionMessage';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { SlashCommand } from '../../utils/slash-commands';
import SlashCommandMenu from '../../components/chat/SlashCommandMenu';
import { getAllModes, ModeConfig } from '../../utils/modes';
import defaultAvatar from '../../assets/images/logo_bg_blue.svg';
import { convertToMentionPath } from '../../../../src/utils/path-mentions';
import { MAX_IMAGES_PER_MESSAGE } from '../locales/types';
import AutoExecuteTool from './AutoExecute';
import OpenAIResourcesIcon from './OpenAIResources';

interface ChatTextAreaAdaptorProps {
  inputValue: string;
  shouldDisableImages: boolean;
  textAreaDisabled: boolean;
  contextMenuContainerRef: React.RefObject<HTMLDivElement>;
  handleMenuMouseDown: () => void;
  placeholderText: string;
  selectedImages: string[];
  setSelectedImages: React.Dispatch<React.SetStateAction<string[]>>;
  onSend: () => void;
  onSelectImages: () => void;
  onHeightChange?: (height: number) => void;
  ref?: React.Ref<HTMLTextAreaElement>;
  textAreaRef?: any;
  highlightLayerRef?: React.RefObject<HTMLDivElement>;
  selectionContextLayerRef?: React.RefObject<HTMLDivElement>;
  contextLayerRef?: React.RefObject<HTMLDivElement>;
  queryItems: any[];
  handleMentionSelect: (type: any, value?: string) => void;
  showContextMenu: boolean;
  setShowContextMenu: (show: boolean) => void;
  searchQuery: string;
  selectedMenuIndex: number;
  setSelectedMenuIndex: (index: number) => void;
  selectedType: any;
  isTextAreaFocused: boolean;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  handleKeyUp: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  thumbnailsHeight: number;
  handleBlur: () => void;
  handlePaste: (e: React.ClipboardEvent) => void;
  updateCursorPosition: () => void;
  updateHighlights: () => void;
  textAreaBaseHeight: number | undefined;
  handleThumbnailsHeightChange: (height: number) => void;
  setIsTextAreaFocused: (e: boolean) => void;
  setTextAreaBaseHeight: (e: number | undefined) => void;
  isStreaming: boolean;
  messages?: any;
  promptList?: any;
  handleSecondaryButtonClick: (value: string, selectedImages: string[], isTerminate?: boolean) => void;
  updateStatus?: string;
  showSlashCommandsMenu?: boolean;
  slashCommandsMenuContainerRef?: React.RefObject<HTMLDivElement>;
  selectedSlashCommandsIndex: number;
  slashCommandsQuery: string;
  setSelectedSlashCommandsIndex: (index: number) => void;
  handleSlashCommandsSelect: (cmd: SlashCommand) => void;
  setShowPromptView?: () => void;
  setInputValue: (value: string) => void;
  setIntendedCursorPosition: (position: number) => void;
  cursorPosition: number;
  setCursorPosition: (position: number) => void;
}
export const ChatTextAreaAdaptor = forwardRef<HTMLTextAreaElement, ChatTextAreaAdaptorProps>(
  (
    {
      showContextMenu,
      contextMenuContainerRef,
      handleMentionSelect,
      searchQuery,
      handleMenuMouseDown,
      selectedMenuIndex,
      setSelectedMenuIndex,
      selectedType,
      queryItems,
      updateStatus,
      isTextAreaFocused,
      highlightLayerRef,
      selectionContextLayerRef,
      contextLayerRef,
      thumbnailsHeight,
      textAreaRef,
      inputValue,
      handleInputChange,
      updateHighlights,
      handleKeyDown,
      handleKeyUp,
      setIsTextAreaFocused,
      handleBlur,
      handlePaste,
      updateCursorPosition,
      textAreaBaseHeight,
      setTextAreaBaseHeight,
      onHeightChange,
      placeholderText,
      textAreaDisabled,
      selectedImages,
      setSelectedImages,
      handleThumbnailsHeightChange,
      shouldDisableImages,
      onSelectImages,
      isStreaming,
      onSend,
      handleSecondaryButtonClick,
      messages,
      slashCommandsMenuContainerRef,
      showSlashCommandsMenu,
      selectedSlashCommandsIndex = 0,
      setSelectedSlashCommandsIndex,
      slashCommandsQuery,
      handleSlashCommandsSelect,
      setShowPromptView,
      promptList,
      setInputValue,
      setIntendedCursorPosition,
      cursorPosition,
      setCursorPosition,
    },
    ref
  ) => {
    const [ChatModeIcon] = useState<Record<string, string>>({
      architect: 'guihua',
      chat: 'wenda',
      code: 'bianma',
      orchestrator: 'tiaoduzhe',
      'design-engineer': 'ui',
      debug: 'ceshi',
      promptsButtonClicked: 'xinzeng',
      resource: 'Aiyingyong',
    });
    const state = useExtensionState();
    const { customModes, mode, cwd } = state;
    const [modelList, setModelList] = useState<ChatModelConfig[]>([]);
    const [modeOpen, setModeOpen] = useState<boolean>(false);
    const [modelOpen, setModelOpen] = useState<boolean>(false);
    const [config, setConfig] = useState<Record<string, any>>({ model: '' });
    const [isShowMenuList, setIsShowMenuList] = useState<boolean>(true);
    // const [shownTooltipMode, setShownTooltipMode] = useState<ChatSettings['mode'] | null>(null);
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    // 拖拽调整高度相关状态
    const [isDragging, setIsDragging] = useState(false);
    const [textAreaHeight, setTextAreaHeight] = useState<number | null>(null);
    const [minHeight, setMinHeight] = useState<number>(0);
    const dragStartY = useRef<number>(0);
    const dragStartHeight = useRef<number>(0);
    const dragDirection = useRef<'up' | 'down'>('up');
    const textAreaContainerRef = useRef<HTMLDivElement>(null);

    // 工具条相关状态
    const [autoExecute, setAutoExecute] = useState(false);
    const [showQuickToolsSettings, setShowQuickToolsSettings] = useState(false);
    const quickToolsSettingsRef = useRef<HTMLDivElement>(null);
    const settingsIconRef = useRef<HTMLDivElement>(null);

    // 从全局状态获取思考模式
    const { thinkingMode, setThinkingMode, webSearchEnabled, setWebSearchEnabled } = useExtensionState();

    // 添加思考模式变化的处理函数
    const handleThinkingModeChange = useCallback(
      (checked: boolean) => {
        console.log('思考模式开关被点击:', checked, '当前状态:', thinkingMode);
        if (checked !== thinkingMode) {
          setThinkingMode(checked);
        }
      },
      [setThinkingMode, thinkingMode]
    );

    // 添加联网搜索变化的处理函数
    const handleWebSearchChange = useCallback(
      (checked: boolean) => {
        console.log('联网搜索开关被点击:', checked, '当前状态:', webSearchEnabled);
        if (checked !== webSearchEnabled) {
          setWebSearchEnabled(checked);
        }
      },
      [setWebSearchEnabled, webSearchEnabled]
    );

    // 处理部署按钮点击
    const handleDeployClick = useCallback(() => {
      // 发送消息到VSCode扩展获取工作空间路径并发送部署消息
      vscode.postMessage({
        type: 'deployProject',
        text: '帮我部署下项目，项目id是'
      });
    }, []);

    // 最大高度设置为250px
    const MAX_HEIGHT = 250;

    // 初始化最小高度
    useEffect(() => {
      if (textAreaBaseHeight && minHeight === 0) {
        setMinHeight(textAreaBaseHeight);
      }
    }, [textAreaBaseHeight, minHeight]);

    // 拖拽开始处理
    const handleDragStart = useCallback((e: React.MouseEvent, direction: 'up' | 'down' = 'up') => {
      e.preventDefault();
      setIsDragging(true);
      dragStartY.current = e.clientY;
      dragDirection.current = direction;

      if (textAreaContainerRef.current) {
        const rect = textAreaContainerRef.current.getBoundingClientRect();
        dragStartHeight.current = rect.height;
        // 注意：这里不立即设置 textAreaHeight，只有在实际拖拽移动时才设置
      }
    }, []);

    // 拖拽过程处理
    const handleDragMove = useCallback(
      (e: MouseEvent) => {
        if (!isDragging) return;

        // 如果还没有设置过高度，使用当前高度作为初始值（只在第一次移动时设置）
        if (textAreaHeight === null && textAreaContainerRef.current) {
          const rect = textAreaContainerRef.current.getBoundingClientRect();
          console.log(rect);
          setTextAreaHeight(rect.height);
          return; // 第一次设置后直接返回，避免立即计算新高度
        }

        const deltaY = e.clientY - dragStartY.current;
        const direction = dragDirection.current;

        let newHeight;
        if (direction === 'up') {
          // 向上拖拽手柄：反转 deltaY，使向上拖拽时增加高度
          newHeight = dragStartHeight.current - deltaY;
        } else {
          // 向下拖拽手柄：正常 deltaY，使向下拖拽时增加高度
          newHeight = dragStartHeight.current + deltaY;
        }

        const currentMinHeight = minHeight || textAreaBaseHeight || 120; // 默认最小高度120px

        // 限制高度范围
        const clampedHeight = Math.max(currentMinHeight, Math.min(MAX_HEIGHT, newHeight));
        setTextAreaHeight(clampedHeight);
      },
      [isDragging, minHeight, textAreaBaseHeight, MAX_HEIGHT, textAreaHeight]
    );

    // 拖拽结束处理
    const handleDragEnd = useCallback(() => {
      setIsDragging(false);
    }, []);

    // 添加全局鼠标事件监听
    useEffect(() => {
      if (isDragging) {
        document.addEventListener('mousemove', handleDragMove);
        document.addEventListener('mouseup', handleDragEnd);
        document.body.style.cursor = 'ns-resize';
        document.body.style.userSelect = 'none';

        return () => {
          document.removeEventListener('mousemove', handleDragMove);
          document.removeEventListener('mouseup', handleDragEnd);
          document.body.style.cursor = '';
          document.body.style.userSelect = '';
        };
      }
    }, [isDragging, handleDragMove, handleDragEnd]);

    const modelAvatar = useMemo(() => {
      return modelList.find((item) => item.label === config.model)?.avatar || DEFAULT_ASSISTANT_AVATAR;
    }, [config.model, modelList]);
    const getPopupContainer = (id: string) => {
      return () => document.getElementById(id) || document.createElement('div');
    };
    const setChatModel = useCallback((label: any) => {
      vscode.postMessage({
        type: 'chatgpt-set-model',
        model: label || '',
      });
    }, []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const updateCoderMode = useCallback(
      (mode: ModeConfig) => {
        if (mode.agentId === 'promptsButtonClicked') {
          setModeOpen(false);
          // 触发VSCode命令打开智能体配置页面
          vscode.postMessage({
            type: 'openSettings',
            text: 'agent',
          });
          return;
        }
        vscode.postMessage({
          type: 'joycoder-set-mode',
          text: mode.agentId,
        });
        setTimeout(() => {
          textAreaRef.current?.focus();
        }, 100);
        setModeOpen(false);
      },
      [setShowPromptView]
    );

    const lastMessage = useMemo(() => messages.at(-1), [messages]);
    const apiRequestFailedMessage =
      lastMessage?.ask === 'api_req_failed' // if request is retried then the latest message is a api_req_retried
        ? lastMessage?.text
        : undefined;
    const [cost, apiReqStreamingFailedMessage] = useMemo(() => {
      if (lastMessage?.text != null && lastMessage?.say === 'api_req_started') {
        const info: JoyCoderApiReqInfo = JSON.parse(lastMessage?.text);
        return [info?.cost, info?.streamingFailedMessage];
      }
      return [undefined, undefined];
    }, [lastMessage?.text, lastMessage?.say]);
    const handleMessage = useCallback(
      (e: MessageEvent) => {
        const message: ExtensionMessage = e.data;
        switch (message.type) {
          case 'updateGPTConfig':
            setModelOpen(false);
            setConfig(message?.modelConfig || {});
            break;
          case 'updateGPTModel':
            const modelList = message?.modelList || [];
            setModelList(modelList);
            break;
          case 'thinkingModeState':
            // 接收思考模式状态
            setThinkingMode(message?.value || false);
            break;
          // case 'updateLoginType':
          //   setJoyCoderEnv(message?.data?.joyCoderEnv || 'inner');
          //   break;
        }
        // textAreaRef.current is not explicitly required here since react gaurantees that ref will be stable across re-renders, and we're not using its value but its reference.
      },
      [setConfig, setModelList]
    );

    const handleDrop = useCallback(
      async (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDraggingOver(false);

        const textFieldList = e.dataTransfer.getData('text');
        const textUriList = e.dataTransfer.getData('application/vnd.code.uri-list');
        // When textFieldList is empty, it may attempt to use textUriList obtained from drag-and-drop tabs; if not empty, it will use textFieldList.
        const text = textFieldList || textUriList;
        if (text) {
          // Split text on newlines to handle multiple files
          const lines = text.split(/\r?\n/).filter((line) => line.trim() !== '');

          if (lines.length > 0) {
            // Process each line as a separate file path
            let newValue = inputValue.slice(0, cursorPosition);
            let totalLength = 0;

            // Using a standard for loop instead of forEach for potential performance gains.
            for (let i = 0; i < lines.length; i++) {
              const line = lines[i];
              // Convert each path to a mention-friendly format
              const mentionText = convertToMentionPath(line, cwd);
              newValue += mentionText;
              totalLength += mentionText.length;

              // Add space after each mention except the last one
              if (i < lines.length - 1) {
                newValue += ' ';
                totalLength += 1;
              }
            }

            // Add space after the last mention and append the rest of the input
            newValue += ' ' + inputValue.slice(cursorPosition);
            totalLength += 1;

            setInputValue(newValue);
            const newCursorPosition = cursorPosition + totalLength;
            setCursorPosition(newCursorPosition);
            setIntendedCursorPosition(newCursorPosition);
          }

          return;
        }

        const files = Array.from(e.dataTransfer.files);

        if (files.length > 0) {
          const acceptedTypes = ['png', 'jpeg', 'webp'];

          const imageFiles = files.filter((file) => {
            const [type, subtype] = file.type.split('/');
            return type === 'image' && acceptedTypes.includes(subtype);
          });

          // 只有当模型支持 vision 功能且未禁用图片时才处理图片拖拽
          const supportsVision = config.modelConfig?.features?.includes('vision');
          if (!shouldDisableImages && supportsVision && imageFiles.length > 0) {
            const imagePromises = imageFiles.map((file) => {
              return new Promise<string | null>((resolve) => {
                const reader = new FileReader();

                reader.onloadend = () => {
                  if (reader.error) {
                    console.error('Error reading file:', reader.error);
                    resolve(null);
                  } else {
                    const result = reader.result;
                    resolve(typeof result === 'string' ? result : null);
                  }
                };

                reader.readAsDataURL(file);
              });
            });

            const imageDataArray = await Promise.all(imagePromises);
            const dataUrls = imageDataArray.filter((dataUrl): dataUrl is string => dataUrl !== null);

            if (dataUrls.length > 0) {
              setSelectedImages((prevImages) => [...prevImages, ...dataUrls].slice(0, MAX_IMAGES_PER_MESSAGE));
            } else {
              console.warn('No valid images were processed');
            }
          } else if (imageFiles.length > 0 && !supportsVision) {
            // 如果拖拽了图片但模型不支持 vision，给出提示
            console.warn('当前模型不支持图片功能，请切换至支持多模态的模型');
          }
        }
      },
      [
        cursorPosition,
        cwd,
        inputValue,
        setInputValue,
        setCursorPosition,
        setIntendedCursorPosition,
        shouldDisableImages,
        setSelectedImages,
        config.modelConfig?.features,
      ]
    );

    useEvent('message', handleMessage);

    // 模式选择菜单项
    const modeMenuItems = useMemo<DropdownMenuItem[]>(() => {
      const allModes = [
        ...getAllModes(customModes).filter((mode) => mode.isActive !== false),
        {
          agentId: 'promptsButtonClicked',
          name: '创建智能体',
          type: 'action',
          category: 'custom',
        },
      ];

      // 按category分组，使用Map保证插入顺序
      const groupedModes = new Map<string, any[]>();

      // 预定义分组顺序，确保system组在前
      const categoryOrder = ['system', 'custom'];
      categoryOrder.forEach((category) => {
        groupedModes.set(category, []);
      });

      // 将模式分配到对应分组
      allModes.forEach((mode) => {
        const category = mode.category || 'custom';
        if (!groupedModes.has(category)) {
          groupedModes.set(category, []);
        }
        groupedModes.get(category)!.push(mode);
      });

      // 按预定义顺序生成菜单项
      return categoryOrder
        .filter((category) => groupedModes.get(category)!.length > 0)
        .map((category) => ({
          key: category,
          type: 'group' as const,
          label: category === 'system' ? '内置智能体' : '自定义智能体',
          children: groupedModes.get(category)!.map((mode: any) => ({
            key: mode.agentId, // 使用agentId作为稳定的key
            label: (
              <CustomTooltip
                color={'var(--vscode-button-secondaryBackground, #72747C)'}
                title={mode?.name}
                placement="top"
                hidden={mode?.agentId === 'promptsButtonClicked' ? true : false}
              >
                <div
                  className={`joycoder-coder-mode-dropdown-option ${mode.agentId === 'promptsButtonClicked' ? 'joycoder-coder-mode-dropdown-option-action' : ''}`}
                >
                  {ChatModeIcon[mode?.agentId] ? (
                    <i
                      style={{ color: 'var(--vscode-editor-foreground)' }}
                      className={`icon iconfont icon-${ChatModeIcon[mode?.agentId] || 'zidingyizhinengtitouxiang'}`}
                    />
                  ) : (
                    <img
                      style={{ width: '16px', marginRight: '4px' }}
                      src={defaultAvatar}
                      alt="zidingyizhinengtitouxiang"
                    />
                  )}
                  <span className="joycoder-coder-mode-dropdown-option-title">{mode?.name}</span>
                </div>
              </CustomTooltip>
            ),
            onClick: () => updateCoderMode(mode),
          })),
        }));
    }, [customModes, ChatModeIcon, updateCoderMode]);

    // 模型选择菜单项
    const modelMenuItems = useMemo<DropdownMenuItem[]>(() => {
      const requireModelFeatures = config.requireModelFeatures;
      const models = modelList.filter((item) => {
        if (requireModelFeatures && requireModelFeatures.length) {
          return requireModelFeatures.every((feature: any) => item.features?.includes(feature));
        }
        return true;
      });
      return models.map((item) => {
        const features = item.features ?? [];
        const ICONS = features.map((feature) => {
          const idKey = `${item.label}_${feature}`;
          if (feature === 'vision') {
            return (
              <Tooltip
                key={idKey}
                title="支持图文多模态"
                getTooltipContainer={getPopupContainer(idKey)}
                placement="topRight"
              >
                <FileImageOutlined id={idKey} className="chatconfig_menu_content_header_icons--vision" />
              </Tooltip>
            );
          }
          if (feature === 'function_call') {
            return (
              <Tooltip
                key={idKey}
                title="支持函数调用（逻辑规划）"
                getTooltipContainer={getPopupContainer(idKey)}
                placement="topRight"
              >
                <ApiOutlined id={idKey} className="chatconfig_menu_content_header_icons--function" />
              </Tooltip>
            );
          }
          if (feature === 'recommend') {
            return (
              <Tooltip
                key={idKey}
                title="JoyCode推荐使用"
                getTooltipContainer={getPopupContainer(idKey)}
                placement="topRight"
              >
                <LikeOutlined id={idKey} className="chatconfig_menu_content_header_icons--recommend" />
              </Tooltip>
            );
          }
          return null;
        });
        if (item.maxTotalTokens > 0) {
          ICONS.push(
            <Tooltip
              key={`${item.label}_maxToken`}
              title={`最大支持上下文长度${(item.maxTotalTokens / 1000).toFixed(0)}K`}
              getTooltipContainer={getPopupContainer(`${item.label}_maxToken`)}
              placement="topRight"
            >
              <div id={`${item.label}_maxToken`} className="chatconfig_menu_content_header_icons--tokens">
                {(item.maxTotalTokens / 1000).toFixed(0)}K
              </div>
            </Tooltip>
          );
        }
        return {
          key: item.label,
          label: (
            <div className="chatconfig_menu">
              <div className="chatconfig_menu_action">
                <img style={{ width: 14, height: 14 }} src={item.avatar || DEFAULT_ASSISTANT_AVATAR} alt={item.label} />
                <div className={`chatconfig_menu_content ${config.model === item.label ? 'active' : ''}`}>
                  <div className="chatconfig_menu_content_header">
                    {item.label}
                    <div className="chatconfig_menu_content_header_icons">{ICONS}</div>
                  </div>
                  <CustomTooltip
                    color={'var(--vscode-button-secondaryBackground, #72747C)'}
                    title={item.description}
                    placement="top"
                    scale={0.7}
                  >
                    <div className="chatconfig_menu_content_tip">{item.description}</div>
                  </CustomTooltip>
                </div>
              </div>
            </div>
          ),
          onClick: () => setChatModel(item.label),
        };
      });
    }, [modelList, config, setChatModel]);

    useEffect(() => {
      if (state?.isIDE) {
        console.log('模型列表', modelMenuItems, config);
        const isShowMenu = !(modelMenuItems.length === 1 && modelMenuItems[0].key === 'default');
        setIsShowMenuList(isShowMenu);

        // 当大模型有数据，且默认模型不在列表中时，切换为第一个
        const defaultMenuItem = modelMenuItems?.find((item) => item.key === config?.model);

        if (modelMenuItems.length > 1 && !defaultMenuItem) {
          console.log('defaultMenuItem---->', defaultMenuItem);
          setChatModel(modelMenuItems[0].key);
        }
      }
    }, [modelMenuItems]);

    useEffect(() => {
      if (!config.modelConfig?.features?.includes('vision')) {
        setSelectedImages([]);
      }
    }, [config, setSelectedImages]);

    // 点击外部关闭快捷工具设置弹框
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;
        const isClickInsidePopup = quickToolsSettingsRef.current && quickToolsSettingsRef.current.contains(target);
        const isClickOnIcon = settingsIconRef.current && settingsIconRef.current.contains(target);

        if (!isClickInsidePopup && !isClickOnIcon) {
          setShowQuickToolsSettings(false);
        }
      };

      if (showQuickToolsSettings) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }
    }, [showQuickToolsSettings]);

    const [highlightPlaceholderHeight, setHighlightPlaceholderHeight] = useState(0);
    const updateHighlightPlaceholderHeight = () => {
      if (contextLayerRef?.current) {
        setHighlightPlaceholderHeight(contextLayerRef.current.offsetHeight + 2);
      }
    };
    useEffect(() => {
      // const allModes = getAllModes(customModes);
      // if (!allModes.find((modeItem) => modeItem.agentId === mode)) {
      //   updateCoderMode(allModes[0].agentId);
      // }

      updateHighlightPlaceholderHeight();
      const resizeObserver = new ResizeObserver(updateHighlightPlaceholderHeight);
      if (contextLayerRef?.current) {
        resizeObserver.observe(contextLayerRef.current);
      }
      return () => {
        if (contextLayerRef?.current) {
          resizeObserver.unobserve(contextLayerRef.current);
        }
      };
    }, []);
    return (
      <div style={{ position: 'relative' }}>
        {/* 工具条 */}
        <div className="joycoder-toolbar">
          <div className="joycoder-toolbar-left">
            <div
              ref={settingsIconRef}
              className="joycoder-toolbar-settings-icon"
              onClick={(e) => {
                e.stopPropagation();
                setShowQuickToolsSettings((prev) => !prev);
              }}
            >
              <CustomTooltip
                  color={'var(--vscode-button-secondaryBackground, #72747C)'}
                  title={'快捷工具设置'}
                  placement="topLeft"
                >
                <i className="icon iconfont icon-tongyongshezhi" />
              </CustomTooltip>
              {/* 快捷工具设置弹框 */}
              {showQuickToolsSettings && (
                <div
                  ref={quickToolsSettingsRef}
                  className="joycoder-quick-tools-settings"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="joycoder-quick-tools-settings-title">快捷工具设置</div>
                  <div className="joycoder-quick-tools-settings-content">
                    <div className="joycoder-quick-tools-settings-item">
                      {/* <span>思考模式 ({thinkingMode ? '开启' : '关闭'})</span> */}
                      <span>思考模式</span>
                      <Switch
                        size="small"
                        checked={Boolean(thinkingMode)}
                        onChange={handleThinkingModeChange}
                        disabled={false}
                      />
                    </div>
                    <div className="joycoder-quick-tools-settings-item">
                      <span>联网搜索</span>
                      <Switch size="small" checked={Boolean(webSearchEnabled)} onChange={handleWebSearchChange} />
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div style={{color: 'var(--vscode-foreground)', opacity: '0.5'}}>|</div>
            <div className="joycoder-toolbar-icon-group">
              <OpenAIResourcesIcon />
              {/* 部署按钮 - 只在远程环境下显示 */}
              {state.isRemoteEnvironment && (
                <CustomTooltip
                  color={'var(--vscode-button-secondaryBackground, #72747C)'}
                  title={'快速部署'}
                  placement="top"
                >
                  <i
                    className="icon iconfont icon-bushu"
                    style={{ cursor: 'pointer' }}
                    onClick={handleDeployClick}
                  />
                </CustomTooltip>
              )}
            </div>
          </div>
          <div className="joycoder-toolbar-right">
            <div
              className="joycoder-toolbar-settings-icon"
              onClick={() => {
                vscode.postMessage({ type: 'clearTask' });
              }}>
              <CustomTooltip
                  color={'var(--vscode-button-secondaryBackground, #72747C)'}
                  title={'新会话'}
                  placement="topRight"
                >
                <i className="icon iconfont icon-xinzenghuihua" />
              </CustomTooltip>
            </div>
          </div>
        </div>

        <div
          className="mention-context-textarea-highlight-layer"
          style={{
            padding: '4px 20px 10px',
            opacity: 1,
            position: 'relative',
            display: 'flex',
          }}
          onDrop={handleDrop}
          onDragOver={(e) => {
            // Only allowed to drop images/files on shift key pressed.
            if (!e.shiftKey) {
              setIsDraggingOver(false);
              return;
            }

            e.preventDefault();
            setIsDraggingOver(true);
            e.dataTransfer.dropEffect = 'copy';
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            const rect = e.currentTarget.getBoundingClientRect();

            if (
              e.clientX <= rect.left ||
              e.clientX >= rect.right ||
              e.clientY <= rect.top ||
              e.clientY >= rect.bottom
            ) {
              setIsDraggingOver(false);
            }
          }}
        >
          {showSlashCommandsMenu && (
            <div ref={slashCommandsMenuContainerRef}>
              <SlashCommandMenu
                onSelect={handleSlashCommandsSelect}
                selectedIndex={selectedSlashCommandsIndex}
                setSelectedIndex={setSelectedSlashCommandsIndex}
                onMouseDown={handleMenuMouseDown}
                query={slashCommandsQuery}
                promptList={promptList}
              />
            </div>
          )}
          {showContextMenu && (
            <div ref={contextMenuContainerRef}>
              <ContextMenu
                onSelect={handleMentionSelect}
                searchQuery={searchQuery}
                onMouseDown={handleMenuMouseDown}
                selectedIndex={selectedMenuIndex}
                setSelectedIndex={setSelectedMenuIndex}
                selectedType={selectedType}
                queryItems={queryItems}
                updateStatus={updateStatus}
              />
            </div>
          )}
          {/* 输入框@高亮 */}
          <div
            className="mention-context-textarea-highlight-layer-bg"
            style={{
              position: 'absolute',
              top: 10,
              left: 20,
              right: 22,
              bottom: 10,
              pointerEvents: 'none',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
              color: 'transparent',
              overflow: 'hidden',
              // backgroundColor: 'var(--vscode-editor-background, #72747C)',
              fontFamily: 'var(--vscode-font-family)',
              fontSize: 'var(--vscode-editor-font-size)',
              lineHeight: 'var(--vscode-editor-line-height)',
              borderRadius: 6,
              borderLeft: 0,
              borderRight: 0,
              borderTop: 0,
              // borderBottom: `${thumbnailsHeight + 6}px solid transparent`,
              borderBottom: '26px solid transparent',
              padding: '4px 6px 3px',
              zIndex: 2,
            }}
          >
            <div style={{ height: highlightPlaceholderHeight }} />
            <div
              ref={highlightLayerRef}
              style={{
                overflow: 'hidden',
                height: '100%',
              }}
            ></div>
          </div>
          {/* 输入框 */}
          <div
            ref={textAreaContainerRef}
            className={`${
              isTextAreaFocused
                ? 'focus mention-context-textarea-highlight-layer-inner'
                : 'mention-context-textarea-highlight-layer-inner'
            } ${isDraggingOver ? 'dragging-over' : ''}`}
            style={{
              height: textAreaHeight ? `${textAreaHeight}px` : undefined,
              minHeight: minHeight ? `${minHeight}px` : undefined,
              maxHeight: `${MAX_HEIGHT}px`,
              overflow: 'hidden',
              position: 'relative',
            }}
          >
            {/* 向上拖拽手柄 */}
            <div
              className="textarea-resize-handle-top"
              onMouseDown={(e) => handleDragStart(e, 'up')}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                cursor: 'ns-resize',
                backgroundColor: 'transparent',
                zIndex: 1001,
              }}
            >
              <div
                style={{
                  position: 'absolute',
                  top: '1px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '30px',
                  height: '2px',
                  backgroundColor: isDragging ? 'var(--vscode-focusBorder)' : 'var(--vscode-input-border)',
                  borderRadius: '1px',
                  transition: 'background-color 0.2s ease',
                }}
              />
            </div>
            <div
              ref={contextLayerRef}
              style={{
                backgroundColor: 'var(--vscode-editor-background, #72747C)',
                overflow: 'hidden',
                borderTopLeftRadius: '6px',
                borderTopRightRadius: '6px',
              }}
            >
              <div className="joycoder-context-box" ref={selectionContextLayerRef} style={{ height: 'auto' }}></div>
              {selectedImages.length > 0 && (
                <Thumbnails
                  images={selectedImages}
                  setImages={setSelectedImages}
                  onHeightChange={handleThumbnailsHeightChange}
                  style={{
                    zIndex: 2,
                  }}
                />
              )}
            </div>
            <DynamicTextArea
              ref={(el: any) => {
                if (typeof ref === 'function') {
                  ref(el);
                } else if (ref) {
                  ref.current = el;
                }
                textAreaRef.current = el;
              }}
              value={inputValue}
              disabled={textAreaDisabled}
              onChange={(e: any) => {
                handleInputChange(e);
                updateHighlights();
              }}
              onKeyDown={handleKeyDown}
              onKeyUp={handleKeyUp}
              onFocus={() => setIsTextAreaFocused(true)}
              onBlur={handleBlur}
              onPaste={handlePaste}
              onSelect={updateCursorPosition}
              onMouseUp={updateCursorPosition}
              onHeightChange={(height: any) => {
                if (textAreaBaseHeight === undefined || height < textAreaBaseHeight) {
                  setTextAreaBaseHeight(height);
                }
                onHeightChange?.(height);
              }}
              placeholder={textAreaDisabled || isStreaming ? '模型输出中···' : placeholderText}
              maxRows={textAreaHeight ? undefined : 10}
              minRows={6}
              autoFocus={true}
              style={{
                // width: 'calc(100% - 2px)',
                width: '100%',
                ...(textAreaHeight && { height: textAreaHeight }),
                boxSizing: 'border-box',
                backgroundColor: 'var(--vscode-editor-background, #72747C)',
                color: 'var(--vscode-editor-foreground, #72747C)',
                borderRadius: 6,
                fontFamily: 'var(--vscode-font-family)',
                fontSize: 'var(--vscode-editor-font-size)',
                lineHeight: 'var(--vscode-editor-line-height)',
                resize: 'none',
                overflowX: 'hidden',
                overflowY: textAreaHeight ? 'auto' : 'scroll',
                scrollbarWidth: 'none',
                borderLeft: 0,
                borderRight: 0,
                borderTop: 0,
                // borderBottom: `${thumbnailsHeight + 6}px solid transparent`,
                borderBottom: '26px solid transparent',
                borderColor: 'transparent',
                padding: '4px 6px 3px',
                flex: textAreaHeight ? 'none' : 1,
                zIndex: 1,
                position: 'relative',
                bottom: '-3px',
                opacity: 1,
                cursor: textAreaDisabled ? 'not-allowed' : undefined,
                borderTopLeftRadius: highlightPlaceholderHeight > 2 ? 0 : '6px',
                borderTopRightRadius: highlightPlaceholderHeight > 2 ? 0 : '6px',
                margin: '-3px 0 0',
              }}
              onScroll={() => updateHighlights()}
              className="joycoder-coder-textarea"
            />
            {/* 向下拖拽手柄 */}
            <div
              className="textarea-resize-handle-bottom"
              onMouseDown={(e) => handleDragStart(e, 'down')}
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: '4px',
                cursor: 'ns-resize',
                backgroundColor: 'transparent',
                zIndex: 1001,
              }}
            >
              <div
                style={{
                  position: 'absolute',
                  bottom: '1px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '30px',
                  height: '2px',
                  backgroundColor: isDragging ? 'var(--vscode-focusBorder)' : 'var(--vscode-input-border)',
                  borderRadius: '1px',
                  transition: 'background-color 0.2s ease',
                }}
              />
            </div>
          </div>
          {/* 工具栏 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'absolute',
              zIndex: '1000',
              justifyContent: 'space-between',
              width: 'calc(100% - 60px)',
              bottom: '18px',
              left: '28px',
              gap: 4,
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: 4,
                overflow: 'hidden',
              }}
            >
              <CustomDropdown
                className="joycoder-coder-mode-dropdown"
                trigger={['click']}
                placement="top"
                disabled={
                  textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                }
                getPopupContainer={() => {
                  return document.querySelector('#tip-modes') || document.createElement('div');
                }}
                onOpenChange={(open) => {
                  setModeOpen(open);
                }}
                menu={{
                  items: modeMenuItems,
                }}
              >
                <Button
                  disabled={
                    textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                  }
                  style={{
                    border: 'none',
                    transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',
                    userSelect: 'none',
                    touchAction: 'manipulation',
                    borderRadius: 2,
                    background: 'var(--vscode-button-secondaryBackground, #72747C)',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    cursor:
                      textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                        ? 'not-allowed'
                        : undefined,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'var(--vscode-button-secondaryForeground, #72747C)',
                  }}
                  size="small"
                  id="tip-modes"
                  className="joycoder-coder-mode-dropdown-button"
                >
                  {ChatModeIcon[getAllModes(customModes)?.find((item) => item.agentId === mode)?.agentId] ? (
                    <i
                      style={{ color: 'var(--vscode-button-secondaryForeground)' }}
                      className={`icon iconfont icon-${ChatModeIcon[mode] || 'zidingyizhinengtitouxiang'}`}
                    />
                  ) : (
                    <img style={{ width: '10px' }} src={defaultAvatar} alt="zidingyizhinengtitouxiang" />
                  )}{' '}
                  <span>{getAllModes(customModes)?.find((item) => item.agentId === mode)?.name}</span>
                  {!modeOpen && (
                    <i
                      style={{ fontSize: '12px', color: 'var(--vscode-button-secondaryForeground, #72747C)' }}
                      className="icon iconfont icon-xiajiantoutianchong"
                    />
                  )}
                  {!!modeOpen && (
                    <i
                      style={{ fontSize: '12px', color: 'var(--vscode-button-secondaryForeground, #72747C)' }}
                      className="icon iconfont icon-shangjiantoutianchong"
                    />
                  )}
                </Button>
              </CustomDropdown>
              {/* 模型切换 - 根据环境和配置条件显示 */}
              {isShowMenuList && (
                <CustomDropdown
                  menu={{ items: modelMenuItems }}
                  placement="top"
                  trigger={['click']}
                  disabled={
                    textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                  }
                  className="joycoder-chatgpt-model-dropdown"
                  getPopupContainer={() => {
                    return document.querySelector('#tip-models') || document.createElement('div');
                  }}
                  onOpenChange={(open) => {
                    setModelOpen(open);
                  }}
                >
                  <Button
                    disabled={
                      textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                    }
                    style={{
                      height: 20,
                      minWidth: 113,
                      border: ' none',
                      transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',
                      userSelect: 'none',
                      touchAction: 'manipulation',
                      borderRadius: 2,
                      background: 'var(--vscode-button-secondaryBackground, #72747C)',
                      fontSize: 9,
                      cursor:
                        textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                          ? 'not-allowed'
                          : undefined,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'var(--vscode-button-secondaryForeground, #72747C)',
                    }}
                    icon={<img className="model-icon" style={{ width: 12, height: 12 }} src={modelAvatar} alt="" />}
                    size="small"
                    id="tip-models"
                    className="joycoder-chatgpt-model-dropdown-button"
                  >
                    {config.model}
                    {!modelOpen && (
                      <i
                        style={{ fontSize: '12px', color: 'var(--vscode-button-secondaryForeground, #72747C)' }}
                        className="icon iconfont icon-xiajiantoutianchong"
                      />
                    )}
                    {!!modelOpen && (
                      <i
                        style={{ fontSize: '12px', color: 'var(--vscode-button-secondaryForeground, #72747C)' }}
                        className="icon iconfont icon-shangjiantoutianchong"
                      />
                    )}
                  </Button>
                </CustomDropdown>
              )}
              {config.modelConfig?.features?.includes('vision') && (
                <div
                  onClick={() => {
                    if (!shouldDisableImages) {
                      onSelectImages();
                    }
                  }}
                  className="input-icon-button"
                >
                  <i
                    style={{
                      fontSize: 12,
                      color: 'var(--vscode-button-secondaryForeground, #72747C)',
                      cursor: textAreaDisabled ? 'not-allowed' : 'pointer',
                    }}
                    className={`${shouldDisableImages ? 'disabled' : ''} icon iconfont icon-tupian`}
                  />
                </div>
              )}
              <AutoExecuteTool />
            </div>
            <div className="joycoder-chatgpt-input-icons">
              {isStreaming || textAreaDisabled ? (
                  <i
                    style={{ fontSize: 14, cursor: 'pointer' }}
                    className="icon iconfont icon-tingzhi1"
                    onClick={() => handleSecondaryButtonClick(inputValue, selectedImages, true)}
                  />
                ) : (
                  <i
                    style={{ fontSize: 14, cursor: textAreaDisabled ? 'not-allowed' : 'pointer' }}
                    className={`${textAreaDisabled ? 'disabled' : ''} icon iconfont icon-xinxifasong`}
                    onClick={() => {
                      if (!textAreaDisabled) {
                        setIsTextAreaFocused(false);
                        onSend();
                      }
                    }}
                  />
                )}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default ChatTextAreaAdaptor;
