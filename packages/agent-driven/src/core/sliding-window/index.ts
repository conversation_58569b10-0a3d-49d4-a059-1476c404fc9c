import { Anthropic } from '@anthropic-ai/sdk';

import {
  getMessagesSinceLastSummary,
  MAX_CONDENSE_THRESHOLD,
  MIN_CONDENSE_THRESHOLD,
  summarizeConversation,
  SummarizeResponse,
} from '../condense';
import { ApiMessage } from '../task-persistence/apiMessages';
import { ApiHandler } from '../../adaptor/api';

/**
 * Default percentage of the context window to use as a buffer when deciding when to truncate
 */
export const TOKEN_BUFFER_PERCENTAGE = 0.1;

/**
 * Counts tokens for user content using the provider's token counting implementation.
 *
 * @param {Array<Anthropic.Messages.ContentBlockParam>} content - The content to count tokens for
 * @param {ApiHandler} apiHandler - The API handler to use for token counting
 * @returns {Promise<number>} A promise resolving to the token count
 */
export async function estimateTokenCount(
  content: Array<Anthropic.Messages.ContentBlockParam>,
  apiHandler: <PERSON><PERSON><PERSON><PERSON><PERSON>
): Promise<number> {
  if (!content || content.length === 0) return 0;
  return apiHandler.countTokens(content);
}

/**
 * Truncates a conversation by removing a fraction of the messages.
 *
 * The first message is always retained, and a specified fraction (rounded to an even number)
 * of messages from the beginning (excluding the first) is removed.
 *
 * @param {ApiMessage[]} messages - The conversation messages.
 * @param {number} fracToRemove - The fraction (between 0 and 1) of messages (excluding the first) to remove.
 * @param {string} taskId - The task ID for the conversation, used for telemetry
 * @returns {ApiMessage[]} The truncated conversation messages.
 */
export function truncateConversation(messages: ApiMessage[], fracToRemove: number, taskId: string): ApiMessage[] {
  const truncatedMessages = [messages[0]];
  const rawMessagesToRemove = Math.floor((messages.length - 1) * fracToRemove);
  const messagesToRemove = rawMessagesToRemove - (rawMessagesToRemove % 2);
  const remainingMessages = messages.slice(messagesToRemove + 1);
  truncatedMessages.push(...remainingMessages);

  return truncatedMessages;
}

/**
 * Conditionally truncates the conversation messages if the total token count
 * exceeds the model's limit, considering the size of incoming content.
 *
 * @param {ApiMessage[]} messages - The conversation messages.
 * @param {number} totalTokens - The total number of tokens in the conversation (excluding the last user message).
 * @param {number} contextWindow - The context window size.
 * @param {number} maxTokens - The maximum number of tokens allowed.
 * @param {ApiHandler} apiHandler - The API handler to use for token counting.
 * @param {boolean} autoCondenseContext - Whether to use LLM summarization or sliding window implementation
 * @param {string} systemPrompt - The system prompt, used for estimating the new context size after summarizing.
 * @returns {ApiMessage[]} The original or truncated conversation messages.
 */

type TruncateOptions = {
  messages: ApiMessage[];
  totalTokens: number;
  contextWindow: number;
  maxTokens?: number | null;
  apiHandler: ApiHandler;
  autoCondenseContext: boolean;
  autoCondenseContextPercent: number;
  systemPrompt: string;
  taskId: string;
  customCondensingPrompt?: string;
  condensingApiHandler?: ApiHandler;
};

type TruncateResponse = SummarizeResponse & { prevContextTokens: number };

/**
 * Conditionally truncates the conversation messages if the total token count
 * exceeds the model's limit, considering the size of incoming content.
 *
 * @param {TruncateOptions} options - The options for truncation
 * @returns {Promise<ApiMessage[]>} The original or truncated conversation messages.
 */
export async function truncateConversationIfNeeded({
  messages,
  totalTokens,
  contextWindow,
  maxTokens,
  apiHandler,
  autoCondenseContext,
  autoCondenseContextPercent,
  systemPrompt,
  taskId,
  customCondensingPrompt,
  condensingApiHandler,
}: TruncateOptions): Promise<TruncateResponse> {
  let error: string | undefined;
  let cost = 0;
  // Calculate the maximum tokens reserved for response
  const reservedTokens = maxTokens || contextWindow * 0.2;
  // Estimate tokens for the last message (which is always a user message)
  const lastMessage = messages[messages.length - 1];
  const lastMessageContent = lastMessage.content;
  const lastMessageTokens = Array.isArray(lastMessageContent)
    ? await estimateTokenCount(lastMessageContent, apiHandler)
    : await estimateTokenCount([{ type: 'text', text: lastMessageContent as string }], apiHandler);
  console.log(
    '%c [ 上下文压缩lastMessageTokens ]-107',
    'font-size:13px; background:pink; color:#bf2c9f;',
    lastMessageTokens
  );

  let prevContextTokens = totalTokens + lastMessageTokens; // 正常来说算到这里就可以了，但是有些模型不返回token，需要继续处理

  const messagesSinceLastSummary = getMessagesSinceLastSummary(messages);
  console.log('%c [ 上下文压缩计算中messages ]-122', 'font-size:13px; background:pink; color:#bf2c9f;', messages);
  console.log(
    '%c [ 上下文压缩计算中messagesSinceLastSummary ]-122',
    'font-size:13px; background:pink; color:#bf2c9f;',
    messagesSinceLastSummary
  );

  if (messagesSinceLastSummary.length > 1 && totalTokens === 0) {
    console.log('%c [ 上下文压缩模型不返回用量，手动计算 ]-118', 'font-size:13px; background:pink; color:#bf2c9f;');
    // Calculate tokens for all messages except the last one
    const prevMessages = messagesSinceLastSummary.slice(0, -1); //计算前面消息的token
    const contextBlocks = [{ role: 'user', content: systemPrompt }, ...prevMessages].flatMap((message) =>
      typeof message.content === 'string' ? [{ text: message.content, type: 'text' as const }] : message.content
    );
    const estimatePrevTotalTokens = await estimateTokenCount(contextBlocks, apiHandler);
    console.log(
      '%c [ 上下文压缩estimatePrevTotalTokens ]-138',
      'font-size:13px; background:pink; color:#bf2c9f;',
      estimatePrevTotalTokens
    );
    prevContextTokens += estimatePrevTotalTokens;
  }
  // Calculate total effective tokens (totalTokens never includes the last message)

  console.log(
    '%c [ 上下文压缩prevContextTokens ]-113',
    'font-size:13px; background:pink; color:#bf2c9f;',
    prevContextTokens
  );

  // Calculate available tokens for conversation history
  // Truncate if we're within TOKEN_BUFFER_PERCENTAGE of the context window
  const allowedTokens = contextWindow * (1 - TOKEN_BUFFER_PERCENTAGE) - reservedTokens;

  // Determine the effective threshold to use
  let effectiveThreshold = autoCondenseContextPercent;

  // If no specific threshold is found for the profile, fall back to global setting

  if (autoCondenseContext) {
    const contextPercent = (100 * prevContextTokens) / contextWindow;
    console.log(
      '%c [ 上下文压缩contextPercent ]-164',
      'font-size:13px; background:pink; color:#bf2c9f;',
      contextPercent
    );
    if (contextPercent >= effectiveThreshold || prevContextTokens > allowedTokens) {
      console.log('%c [ 上下文压缩-判定为需要压缩 ]-136', 'font-size:13px; background:pink; color:#bf2c9f;');
      // Attempt to intelligently condense the context
      const result = await summarizeConversation(
        messages,
        apiHandler,
        systemPrompt,
        taskId,
        prevContextTokens,
        true, // automatic trigger
        customCondensingPrompt,
        condensingApiHandler
      );
      console.log(
        '%c [ 上下文压缩-判定为需要压缩-result ]-139',
        'font-size:13px; background:pink; color:#bf2c9f;',
        result
      );

      if (result.error) {
        error = result.error;
        cost = result.cost;
      } else {
        return { ...result, prevContextTokens };
      }
    }
  }

  // Fall back to sliding window truncation if needed
  if (prevContextTokens > allowedTokens) {
    const truncatedMessages = truncateConversation(messages, 0.5, taskId);
    console.log(
      '%c [ 上下文压缩Fall back to sliding window truncation truncatedMessages ]-149',
      'font-size:13px; background:pink; color:#bf2c9f;',
      truncatedMessages
    );
    return { messages: truncatedMessages, prevContextTokens, summary: '', cost, error };
  }
  // No truncation or condensation needed
  return { messages, summary: '', cost, prevContextTokens, error };
}

//下面是旧的代码
/*
We can't implement a dynamically updating sliding window as it would break prompt cache
every time. To maintain the benefits of caching, we need to keep conversation history
static. This operation should be performed as infrequently as possible. If a user reaches
a 200k context, we can assume that the first half is likely irrelevant to their current task.
Therefore, this function should only be called when absolutely necessary to fit within
context limits, not as a continuous process.
*/
// export function truncateHalfConversation(
// 	messages: Anthropic.Messages.MessageParam[],
// ): Anthropic.Messages.MessageParam[] {
// 	// API expects messages to be in user-assistant order, and tool use messages must be followed by tool results. We need to maintain this structure while truncating.

// 	// Always keep the first Task message (this includes the project's file structure in environment_details)
// 	const truncatedMessages = [messages[0]]

// 	// Remove half of user-assistant pairs
// 	const messagesToRemove = Math.floor(messages.length / 4) * 2 // has to be even number

// 	const remainingMessages = messages.slice(messagesToRemove + 1) // has to start with assistant message since tool result cannot follow assistant message with no tool use
// 	truncatedMessages.push(...remainingMessages)

// 	return truncatedMessages
// }

/*
getNextTruncationRange: Calculates the next range of messages to be "deleted"
- Takes the full messages array and optional current deleted range
- Always preserves the first message (task message)
- Removes 1/2 of remaining messages (rounded down to even number) after current deleted range
- Returns [startIndex, endIndex] representing inclusive range to delete

getTruncatedMessages: Constructs the truncated array using the deleted range
- Takes full messages array and optional deleted range
- Returns new array with messages in deleted range removed
- Preserves order and structure of remaining messages

The range is represented as [startIndex, endIndex] where both indices are inclusive
The functions maintain the original array integrity while allowing progressive truncation
through the deletedRange parameter

Usage example:
const messages = [user1, assistant1, user2, assistant2, user3, assistant3];
let deletedRange = getNextTruncationRange(messages); // [1,2] (assistant1,user2)
let truncated = getTruncatedMessages(messages, deletedRange);
// [user1, assistant2, user3, assistant3]

deletedRange = getNextTruncationRange(messages, deletedRange); // [2,3] (assistant2,user3)
truncated = getTruncatedMessages(messages, deletedRange);
// [user1, assistant3]
*/
