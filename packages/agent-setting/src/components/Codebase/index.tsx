import React from 'react';
import { But<PERSON>, Progress, Popconfirm, Dropdown, Collapse } from 'antd';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';
import DocList from './docList';
import { CommonMessage } from '../../messages/messageTypes';

interface knowledgeBase {
  name: string;
  dataset_id?: string;
  permission?: string;
  update_date?: string;
  tenant_id?: string;
  status?: string;
  description?: string;
  update_time: number;
  create_time?: number;
  token_num?: number;
}
interface CodebaseTabProps {
  hasWorkspaceFolder: boolean;
  codebaseIndexingProgress: number;
  codebaseIndexingStatus: CodebaseIndexingStatus;
  codebaseIndexingButtonDisabled: boolean;
  onCodebaseIndexing: (action: string) => () => void;
  openLocalPopup: (args: any) => void;
  openUrlPopup: (args: any) => void;
  editKnowledgeBase: (args: any) => void;
  delKnowledgeBase: (args: any) => void;
  knowledgeBaseList: knowledgeBase[];
}

export default function CodebaseTab({
  hasWorkspaceFolder,
  codebaseIndexingProgress,
  codebaseIndexingStatus,
  codebaseIndexingButtonDisabled,
  onCodebaseIndexing,
  openLocalPopup,
  openUrlPopup,
  editKnowledgeBase,
  delKnowledgeBase,
  knowledgeBaseList,
}: CodebaseTabProps) {
  const ragEditListData = () => {
    return [
      {
        key: 'url',
        label: <span onClick={openUrlPopup}>通过URL添加</span>,
      },
      {
        key: 'local',
        label: <span onClick={openLocalPopup}>通过本地文件添加</span>,
      },
    ];
  };
  /**
   * 编辑RAG数据
   * @param params - 编辑参数
   */
  const editRagData = (event: React.MouseEvent<HTMLSpanElement, MouseEvent>, params: any) => {
    event.stopPropagation();
    editKnowledgeBase(params);
  };

  // 删除知识库
  const delRagData = (event: React.MouseEvent<HTMLSpanElement, MouseEvent>, params: any) => {
    event.stopPropagation();
    delKnowledgeBase(params);
  };
  const actionListData = (params: any) => {
    return [
      {
        key: 'edit',
        label: <span onClick={(e) => editRagData(e, params)}>编辑</span>,
      },
      {
        key: 'del',
        label: <span onClick={(e) => delRagData(e, params)}>删除</span>,
      },
    ];
  };

  const handleCollapseChange = (key: string | string[]) => {
    console.log(key);
  };

  const getDocList = (params?: any) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'getDocInfo',
        dataType: 'list',
        data: {
          knowledgeBase: { ...params, dataset_id: params?.dataset_id || params?.id },
        },
      },
    });
    // setDocPopup(true);
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date?.toLocaleString('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">上下文</div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title">代码库索引</div>
        <div className="joycoder-setting-box-text l-20 mt-10">
          构建代码库索引，可以增强智能体对代码库的理解，提升生成效果。构建索引成功后，智能体将自主获取代码库索引；必要时您也可以通过
          @Codebase 的方式要求智能体使用它。
        </div>
        {hasWorkspaceFolder && (
          <>
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING && (
              <div className="joycoder-loading-dots mt-16">索引中({codebaseIndexingProgress}%)</div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
              <div className="joycoder-setting-box-msg mt-16">
                <i className="icon iconfont icon-tanhao mr-4"></i>
                代码库索引未构建
              </div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING && (
              <div className="joycoder-loading-dots mt-16">Codebase服务准备中</div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.NO_WORKSPACE && (
              <div className="joycoder-setting-box-msg mt-16">
                <i className="icon iconfont icon-tanhao mr-4"></i>
                未检测到工作目录，请打开文件夹后重试
              </div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && <div className="mt-16">索引完成</div>}
            {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
              codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED) && (
              <Progress
                percent={codebaseIndexingProgress}
                type="line"
                status={
                  codebaseIndexingProgress === 100 && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED
                    ? 'success'
                    : 'active'
                }
                showInfo={false}
              />
            )}

            {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
              <Button
                type="default"
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                disabled={codebaseIndexingButtonDisabled}
                onClick={onCodebaseIndexing('start')}
              >
                开始索引
              </Button>
            )}
            {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
              codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING) && (
              <Button
                type="default"
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                onClick={onCodebaseIndexing('cancel')}
                disabled={codebaseIndexingButtonDisabled}
              >
                取消索引
              </Button>
            )}

            {/* 重新索引 */}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
              <Button
                type="default"
                onClick={onCodebaseIndexing('start')}
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                disabled={codebaseIndexingButtonDisabled}
              >
                重新索引
              </Button>
            )}

            {/* 删除索引 */}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
              <Popconfirm
                title="确定清除索引吗？"
                onConfirm={onCodebaseIndexing('remove')}
                okText="删除"
                cancelText="取消"
              >
                <Button
                  type="default"
                  style={{
                    backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                    borderColor: 'var(--vscode-input-border, #72747c)',
                    color: 'var(--vscode-button-secondaryForeground, #72747c)',
                    width: '72px',
                    height: '28px',
                    marginTop: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    padding: '0',
                  }}
                >
                  删除索引
                </Button>
              </Popconfirm>
            )}
          </>
        )}
        {!hasWorkspaceFolder && (
          <div className="joycoder-setting-box-msg mt-16">
            <i className="icon iconfont icon-tanhao mr-4"></i>
            未检测到工作目录，请打开文件夹后重试
          </div>
        )}
      </div>
      <div className="joycoder-setting-box mt-20">
        <div className="joycoder-setting-box-title joycoder-rag-title">
          <span>知识库</span>
          <Button
            type="default"
            onClick={editKnowledgeBase}
            style={{
              backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
              borderColor: 'var(--vscode-input-border, #72747c)',
              color: 'var(--vscode-button-secondaryForeground, #72747c)',
              borderRadius: '4px',
              fontSize: '12px',
              padding: '6px 12px',
            }}
          >
            添加知识库
          </Button>
        </div>
        <div className="joycoder-setting-box-text l-20 mt-10">
          通过URL、本地上传等方式添加知识库，构建成功后可增强智能体的知识理解能力。您可以通过 @docs
          主动调用知识库内容，智能体也会在需要时自主获取相关信息。
        </div>
        <div className="joycoder-setting-box-text mt-10">
          <div className="joycoder-rag-list">
            <Collapse onChange={handleCollapseChange}>
              {knowledgeBaseList?.map((knowledgeBase: knowledgeBase, i: number) => {
                return (
                  <Collapse.Panel
                    header={
                      <div className="joycoder-rag-list-item">
                        <div className="joycoder-rag-list-item-name">
                          <div className="item-title">{knowledgeBase.name}</div>
                          <div>更新于{formatDate(knowledgeBase.update_time)}</div>
                        </div>
                        <div className="joycoder-rag-list-item-action">
                          <Dropdown placement="bottom" menu={{ items: ragEditListData() }}>
                            <i className="icon iconfont icon-shangchuan"></i>
                          </Dropdown>
                          {/* <i className="icon iconfont icon-chakanshili" onClick={() => openDocList(knowledgeBase)}></i> */}
                          <Dropdown placement="bottom" menu={{ items: actionListData(knowledgeBase) }}>
                            <i className="icon iconfont icon-gengduo"></i>
                          </Dropdown>
                        </div>
                      </div>
                    }
                    key={i}
                  >
                    <DocList docList={[]} />
                  </Collapse.Panel>
                );
              })}
            </Collapse>
          </div>
          {knowledgeBaseList?.length === 0 && (
            <div className="joycoder-rag-no-data">
              <div>
                <i className="icon iconfont icon-shangxiawen"></i>
                <div className="no-data-text">暂无知识库文档</div>
                <div>
                  点击 <span className="link-text">添加知识库</span> 进行添加
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
