import { type ClientOptions } from 'openai';
import { type BaseLLMParams } from '@langchain/core/language_models/llms';
import { OpenAI } from '@langchain/openai';
import type { OpenAIInput, LegacyOpenAIInput } from '@langchain/openai';
import { getExtParams } from '../shared/utils';
import { BASE_URL } from '../shared/constants';
import { getJdhLoginInfo } from '@joycoder/shared/src/loginJdh';

/**
 * 这是一个暂未完全实现的版本，请使用chat_models代替
 * 暂时不封装LLM类，原因是：@langchain/openai 中的 OpenAI 调用的是已废弃接口v1/completions，我们并未实现这个接口
 */
export class JoyCoderAI extends OpenAI {
  constructor(
    fields?: Partial<OpenAIInput> &
      BaseLLMParams & {
        configuration?: ClientOptions & LegacyOpenAIInput;
      }
  ) {
    const newFields = fields ? { ...fields } : {};

    // 绕过ChatOpenAI内部校验
    newFields.openAIApiKey = 'not-empty';

    // 要透传给接口的扩展参数放在modelKwargs中
    newFields.modelKwargs = {
      ...newFields.modelKwargs,
      ...getJdhLoginInfo(),
      sourceType: 'joyCoderFe',
      extParams: getExtParams(),
    };

    // 设置服务URL
    newFields.configuration = {
      ...newFields.configuration,
      baseURL: BASE_URL,
    };

    super(newFields);
  }
}
