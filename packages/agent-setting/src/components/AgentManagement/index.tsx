import React, { useState, useCallback, useEffect } from 'react';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Radio, Checkbox, Row, Col, Collapse, Avatar, List, Tag, Dropdown } from 'antd';
import { ModeConfig, TOOL_GROUPS, ToolGroup, GroupEntry } from '@joycoder/agent-driven/web-agent/src/utils/modes';
import { IUserPrompt } from '@joycoder/agent-driven/src/core/user-pormpt';
import { CommonMessage } from '../../messages/messageTypes';
import { ModesInfo } from '../../typings/modes';
import { generateRandomId } from '../../utils/utils';
import { useHandleMessage } from '../../hooks/useHandleMessage';
import './index.scss';
import TabButton from '../common/TabButton';
import AgentList from './AgentList';
import AgentMarket from './AgentMarket';
import defaultAvatar from '../../assets/images/logo_bg_blue.svg';

const { TextArea } = Input;
const { Group: RadioGroup, Button: RadioButton } = Radio;
const { Panel } = Collapse;

interface AgentManagementProps {
  autoApprovalSettings: any;
  modesInfo: ModesInfo;
  setAutoApprovalSettings: (settings: any) => void;
  isRemoteEnvironment: boolean;
}

export default function AgentManagement(props: AgentManagementProps): JSX.Element {
  const { autoApprovalSettings, modesInfo, setAutoApprovalSettings, isRemoteEnvironment } = props;

  const [isShowAgentModel, setIsShowAgentModel] = useState(false);
  const [agentTile, setAgentTile] = useState('新建智能体');
  const [newModeName, setNewModeName] = useState('');
  const [newModeSlug, setNewModeSlug] = useState('');
  const [newModeRoleDefinition, setNewModeRoleDefinition] = useState('');
  const [newModeWhenToUse, setNewModeWhenToUse] = useState('');
  const [newModeCustomInstructions, setNewModeCustomInstructions] = useState('');
  const [newModeGroups, setNewModeGroups] = useState<GroupEntry[]>([]);
  const [newModeSource, setNewModeSource] = useState<'global' | 'project'>('global');
  const [agentHeadImg, setAgentHeadImg] = useState(null);
  const [currentAgentInfo, setCurrentAgentInfo] = useState<ModeConfig>({} as ModeConfig);
  const [activeTab, setActiveTab] = useState('list');

  // 快捷指令相关状态
  const [isShowShortcutModal, setIsShowShortcutModal] = useState(false);
  const [isProjectPrompt, setIsProjectPrompt] = useState(isRemoteEnvironment ? true : false);
  const [shortcutActiveTab, setShortcutActiveTab] = useState('list');
  const [shortcutTitle, setShortcutTitle] = useState('新建快捷指令');

  const [projectShortcuts, setProjectShortcuts] = useState<any[]>([]);
  const [globalShortcuts, setGlobalShortcuts] = useState<any[]>([]);
  const [oldSource, setOldSource] = useState<'global' | 'project'>('global');

  // 快捷指令表单状态
  const [shortcutFormData, setShortcutFormData] = useState({
    label: '',
    name: '',
    description: '',
    prompt: '',
    // source: 'global',
  });
  // 存储正在编辑的快捷指令的原始状态
  const [currentEditingShortcut, setCurrentEditingShortcut] = useState<any>(null);

  // 快捷指令验证错误状态
  const [shortcutLabelError, setShortcutLabelError] = useState<string>('');
  const [shortcutNameError, setShortcutNameError] = useState<string>('');

  // 加载快捷指令数据
  useEffect(() => {
    vscode.postMessage({
      type: 'COMMON',
      payload: {
        type: 'get-user-prompts',
        data: {},
      },
    });
  }, []);

  // 使用消息处理钩子获取快捷指令消息
  useHandleMessage((payload: any) => {
    // console.log('Received message:\n\n\n\n', payload);
    if (payload.type === 'user-prompts-response') {
      const { data, source } = payload;
      if (source === 'project') {
        setProjectShortcuts(data || []);
      } else if (source === 'global') {
        setGlobalShortcuts(data || []);
      }
      // console.log('Shortcuts data length:', data?.length, 'source:', source);
    }
  });

  // 处理编辑快捷指令
  const handleEditShortcut = (shortcut: any) => {
    setShortcutTitle('编辑快捷指令');
    const { _source, ...cleanShortcut } = shortcut;
    // const cleanShortcutAsUserPrompt: IUserPrompt = cleanShortcut as IUserPrompt;
    setCurrentEditingShortcut(cleanShortcut);
    setShortcutFormData({
      label: shortcut.label || '',
      name: shortcut.name || '',
      description: shortcut.description || '',
      prompt: shortcut.prompt || '',
    });
    setIsProjectPrompt(isRemoteEnvironment ? true : _source === 'project');
    setOldSource(_source);
    setIsShowShortcutModal(true);
  };

  // 处理删除快捷指令
  const handleDeleteShortcut = (shortcut: any) => {
    const { _source, ...cleanShortcut } = shortcut;
    const cleanShortcutAsUserPrompt: IUserPrompt = cleanShortcut as IUserPrompt;
    // console.log('delete\n\n\n\n' + JSON.stringify(cleanShortcut));
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'delete-user-prompt',
        data: cleanShortcutAsUserPrompt,
        source: _source,
      },
    });
    if (isRemoteEnvironment) {
      setTimeout(() => {
        vscode.postMessage({
          type: 'COMMON',
          payload: {
            type: 'get-user-prompts',
            data: {},
          },
        });
      }, 100);
    }
  };

  // 验证快捷指令是否重复
  const validateShortcutDuplicate = () => {
    // 重置错误状态
    setShortcutLabelError('');
    setShortcutNameError('');

    let isValid = true;
    const { label, name } = shortcutFormData;

    if (!label?.trim()) {
      setShortcutLabelError('请输入标题');
      isValid = false;
    }

    if (!name?.trim()) {
      setShortcutNameError('请输入名称');
      isValid = false;
    }

    if (!isValid) {
      return false;
    }

    // 合并所有快捷指令进行重复检查
    const allShortcuts = [
      ...projectShortcuts.map((item) => ({ ...item, _source: 'project' })),
      ...globalShortcuts.map((item) => ({ ...item, _source: 'global' })),
    ];

    // 编辑模式下，排除当前正在编辑的快捷指令
    const filteredShortcuts = currentEditingShortcut
      ? allShortcuts.filter((item) => !(item.name === currentEditingShortcut.name && item._source === oldSource))
      : allShortcuts;

    const labelExists = filteredShortcuts.some(
      (item) => item.label?.trim().toLowerCase() === label.trim().toLowerCase()
    );

    if (labelExists) {
      setShortcutLabelError('标题已存在，请使用其他标题');
      isValid = false;
    }

    const nameExists = filteredShortcuts.some((item) => item.name?.trim().toLowerCase() === name.trim().toLowerCase());
    if (nameExists) {
      setShortcutNameError('名称已存在，请使用其他名称');
      isValid = false;
    }

    return isValid;
  };

  // 编辑/保存快捷指令
  const handleSaveShortcut = () => {
    if (!validateShortcutDuplicate()) {
      return; // 重名验证失败，停留在模态框
    }

    const shortcutData: IUserPrompt = {
      ...shortcutFormData,
    };
    // 编辑模式
    if (currentEditingShortcut) {
      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'update-user-prompt',
          data: {
            oldShortcut: currentEditingShortcut,
            newShortcut: shortcutData,
          },
          source: oldSource,
          changeSource: (isProjectPrompt ? 'project' : 'global') !== oldSource,
        },
      });
    } else {
      // 新建模式
      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'save-user-prompt',
          data: shortcutData,
          source: isProjectPrompt ? 'project' : 'global',
        },
      });

      // console.log('send\n\n\n\nsend' + JSON.stringify(shortcutData));
    }
    if (isRemoteEnvironment) {
      setTimeout(() => {
        vscode.postMessage({
          type: 'COMMON',
          payload: {
            type: 'get-user-prompts',
            data: {},
          },
        });
      }, 100);
    }

    // 重置表单和关闭模态框
    resetShortcutForm();
    setIsShowShortcutModal(false);
  };

  // 重置快捷指令表单
  const resetShortcutForm = () => {
    setShortcutFormData({
      label: '',
      name: '',
      description: '',
      prompt: '',
      // source: isProjectPrompt ? 'project' : 'global',
    });
    setCurrentEditingShortcut(null);
    setShortcutTitle('新建快捷指令');
    // 重置错误状态
    setShortcutLabelError('');
    setShortcutNameError('');
  };

  const [nameError, setNameError] = useState<string>('');
  const [slugError, setSlugError] = useState<string>('');
  const [roleDefinitionError, setRoleDefinitionError] = useState<string>('');
  const [groupsError, setGroupsError] = useState<string>('');

  const availableGroups = (Object.keys(TOOL_GROUPS) as ToolGroup[]).filter(
    (group) => !TOOL_GROUPS[group].alwaysAvailable
  );

  const groupNames: Record<ToolGroup, string> = {
    read: '读取文件',
    edit: '编辑文件',
    browser: '浏览器',
    command: '运行命令',
    mcp: 'MCP服务',
    modes: '模式',
  };

  const handleNameChange = (name: string) => {
    setNewModeName(name);
    if (!newModeSlug) {
      setNewModeSlug(generateRandomId());
    }
  };

  const updateCustomMode = useCallback((agentId: string, modeConfig: ModeConfig) => {
    const source = modeConfig.source || 'global';
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'updateCustomMode',
          agentId,
          modeConfig: {
            ...modeConfig,
            source,
          },
        },
      },
    });
  }, []);

  const switchMode = useCallback((agentId: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'joycoder-set-mode',
        data: {
          text: agentId,
        },
      },
    });
  }, []);

  const onCreateMode = (newMode: ModeConfig): void => {
    updateCustomMode(newMode.agentId, newMode);
    switchMode(newMode.agentId);
    setIsShowAgentModel(false);
  };

  const handleCreateAgent = () => {
    setNameError('');
    setSlugError('');
    setRoleDefinitionError('');
    setGroupsError('');

    let isValid = true;

    if (!newModeName?.trim()) {
      setNameError('请输入智能体名称');
      isValid = false;
    }

    // if (!newModeSlug?.trim()) {
    //   setSlugError('请输入有效的 agentId');
    //   isValid = false;
    // }

    if (!newModeRoleDefinition?.trim()) {
      setRoleDefinitionError('请输入角色定义');
      isValid = false;
    }

    if (newModeGroups.length === 0) {
      setGroupsError('请至少选择一个工具');
      isValid = false;
    }

    if (isValid) {
      onCreateMode({
        name: newModeName?.trim(),
        agentId: newModeSlug?.trim(),
        agentDefinition: newModeRoleDefinition?.trim(),
        whenToUse: newModeWhenToUse?.trim() || undefined,
        customInstructions: newModeCustomInstructions?.trim() || undefined,
        groups: newModeGroups,
        source: newModeSource,
        isActive: true,
      });
      setIsShowAgentModel(false);
    }
  };

  const resetModalInfo = () => {
    setNewModeName('');
    setNewModeSlug('');
    setNewModeRoleDefinition('');
    setNewModeWhenToUse('');
    setNewModeCustomInstructions('');
    setNewModeGroups([]);
    setNewModeSource('global');
    setNameError('');
    setSlugError('');
    setRoleDefinitionError('');
    setGroupsError('');
    setCurrentAgentInfo({} as ModeConfig);
  };

  const handleEditorAgent = (agentId: string) => {
    const currentAgentInfo =
      modesInfo?.customModes.find((m: ModeConfig) => m.agentId === agentId) || ({} as ModeConfig);
    setCurrentAgentInfo(currentAgentInfo);

    setAgentTile('编辑智能体');
    setNewModeName(currentAgentInfo.name);
    setNewModeSlug(currentAgentInfo.agentId);
    setNewModeRoleDefinition(currentAgentInfo.agentDefinition);
    setNewModeWhenToUse(currentAgentInfo.whenToUse ?? '');
    setNewModeCustomInstructions(currentAgentInfo.customInstructions ?? '');
    setNewModeGroups(currentAgentInfo.groups);
    setNewModeSource(currentAgentInfo.source);

    setIsShowAgentModel(true);
  };

  const handleDelAgent = (agentId: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'deleteCustomMode',
          agentId,
        },
      },
    });
  };

  const handleToggleAgent = (agentId: string, isActive: boolean) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'toggleCustomMode',
          agentId,
          isActive,
        },
      },
    });
  };

  const handleOpenFile = (filePath: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'openFile',
          filePath: `.joycode/${filePath}`,
          agentTile: agentTile,
        },
      },
    });
  };

  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">智能体</div>
      <div className="joycoder-setting-box">
        <div className="agent-tab-buttons">
          <div style={{ marginRight: '15px' }}>
            <TabButton active={activeTab === 'list'} onClick={() => setActiveTab('list')}>
              智能体列表
            </TabButton>
          </div>
          {/* <TabButton active={activeTab === 'market'} onClick={() => setActiveTab('market')}>
            智能体市场
          </TabButton> */}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setAgentTile('新建智能体');
              resetModalInfo();
              setIsShowAgentModel(true);
            }}
            style={{
              backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
              borderColor: 'var(--vscode-input-border, #72747c)',
              width: '106px',
              height: '28px',
              borderRadius: '4px',
              color: 'var(--vscode-button-secondaryForeground, #72747c)',
              fontSize: '12px',
              padding: '0',
              float: 'right',
              marginLeft: 'auto',
            }}
          >
            新建智能体
          </Button>
        </div>
        {activeTab === 'list' && (
          <AgentList
            modesInfo={modesInfo}
            autoApprovalSettings={autoApprovalSettings}
            setAutoApprovalSettings={setAutoApprovalSettings}
            onEditAgent={handleEditorAgent}
            onDeleteAgent={handleDelAgent}
            onToggleAgent={handleToggleAgent}
          />
        )}
        {activeTab === 'market' && <AgentMarket />}
      </div>

      {/* 新建智能体 */}
      <Modal
        title={agentTile}
        open={isShowAgentModel}
        onCancel={() => {
          resetModalInfo();
          setIsShowAgentModel(false);
        }}
        onOk={handleCreateAgent}
        destroyOnClose={true}
        okText="保存"
        cancelText="取消"
        style={{ backgroundColor: '#18181BFF', top: '0' }}
        width={640}
      >
        <div className="agent-info-top">
          <Avatar className="agent-list-avatar fl mr-8" src={agentHeadImg || defaultAvatar} />
          <div className="agent-info-top-title fl">
            <div className="agent-info-top-title-text">
              名称 <span style={{ color: 'red' }}>*</span>
            </div>
            <Input
              placeholder="输入智能体名称"
              defaultValue={currentAgentInfo.name}
              onChange={(e) => handleNameChange(e.target.value)}
              className="agent-info-top-title-input"
              showCount
              maxLength={50}
            />
            <div className="agent-info-top-subtitle color-red">{nameError}</div>
            {/* <div className="agent-info-top-subtitle color-red">{slugError}</div> */}
          </div>
          {/* <div className="agent-info-top-config">
            <a href="https://inner-joycoder.jd.com/" target="_blank">
              <i className="icon iconfont icon-wenhao mr-4"></i>如何配置
            </a>
          </div> */}
        </div>

        <div className="joycoder-setting-box-title mt-24">角色定义</div>
        <div className="agent-info-top-subtitle">
          设定专业方向
          {agentTile === '编辑智能体' && currentAgentInfo.source === 'project' && (
            <>
              ，使用MD：
              <code
                className="agent-info-top-subtitle-code"
                onClick={() =>
                  handleOpenFile(`modes/rules-${currentAgentInfo.agentId || newModeSlug}/agentDefinition.md`)
                }
              >
                编辑
              </code>
            </>
          )}
        </div>
        <TextArea
          className="joycoder-setting-textarea mt-4"
          defaultValue={currentAgentInfo.agentDefinition}
          onChange={(e) => {
            setNewModeRoleDefinition(e.target.value);
          }}
          placeholder="请输入角色定义"
          autoSize={{ minRows: 3, maxRows: 6 }}
        />
        <div className="agent-info-top-subtitle color-red">{roleDefinitionError}</div>

        {/* <div className="joycoder-setting-box-title mt-24">使用场景（可选）</div>
        <div className="agent-info-top-subtitle">清晰描述此模式最适合的场景和任务类型</div>
        <TextArea
          className="joycoder-setting-textarea mt-4"
          defaultValue={currentAgentInfo.whenToUse}
          onChange={(e) => {
            setNewModeWhenToUse(e.target.value);
          }}
          placeholder="请输入使用场景"
          autoSize={{ minRows: 3, maxRows: 6 }}
        /> */}

        <div className="joycoder-setting-box-title mt-24">自定义指令（可选）</div>
        <div className="agent-info-top-subtitle">
          设置专属规则
          {agentTile === '编辑智能体' && currentAgentInfo.source === 'project' && (
            <>
              ，使用MD：
              <code
                className="agent-info-top-subtitle-code"
                onClick={() =>
                  handleOpenFile(`modes/rules-${currentAgentInfo.agentId || newModeSlug}/customInstructions.md`)
                }
              >
                编辑
              </code>
            </>
          )}
        </div>
        <TextArea
          className="joycoder-setting-textarea mt-4"
          defaultValue={currentAgentInfo.customInstructions}
          onChange={(e) => setNewModeCustomInstructions(e.target.value)}
          placeholder="请输入自定义指令"
          autoSize={{ minRows: 3, maxRows: 6 }}
        />

        <div className="joycoder-setting-box-title mt-26">工具</div>
        <div className="agent-info-top-subtitle">为你的智能体配置工具，智能体将根据任务自动使用它们</div>
        <div className="agent-info-top-box mt-8">
          <div className="joycoder-setting-box-title">内置</div>
          <Checkbox.Group
            defaultValue={currentAgentInfo.groups as string[]} //FIXME: ModeConfig.groups不止是一个string[]，而是支持扩展复杂的数据结构，但目前来说，自定义智能体的groups就是一个string[]，暂时这样处理，之后需要修复
            onChange={(checkedValues) => setNewModeGroups(checkedValues as GroupEntry[])}
            className="w-full mt-8"
          >
            <Row gutter={[16, 8]}>
              {availableGroups.map((group) => (
                <Col span={8} key={group}>
                  <Checkbox value={group}>{groupNames[group]}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>

        <div className="agent-info-top-subtitle color-red">{groupsError}</div>

        <Collapse className="mt-24" ghost>
          <Panel header="高级设置" key="1">
            <div className="joycoder-setting-box-title">作用范围</div>
            <RadioGroup
              defaultValue={newModeSource || (isRemoteEnvironment ? 'project' : 'global')}
              onChange={(e) => setNewModeSource(e.target.value)}
              disabled={agentTile === '编辑智能体'}
            >
              {!isRemoteEnvironment && <RadioButton value="global">全局可用</RadioButton>}
              <RadioButton value="project">当前项目</RadioButton>
            </RadioGroup>
          </Panel>
        </Collapse>
      </Modal>

      {/* 快捷指令部分 */}
      <div className="setting-tabs-item-title">快捷指令</div>
      <div className="joycoder-setting-box">
        <div className="agent-tab-buttons">
          <div style={{ marginRight: '15px', display: 'flex', alignItems: 'center', gap: '8px' }}>
            <TabButton active={shortcutActiveTab === 'list'} onClick={() => setShortcutActiveTab('list')}>
              快捷指令列表
            </TabButton>
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              style={{
                // color: '#999',
                border: 'none',
                padding: '4px',
                width: 'auto',
                height: 'auto',
              }}
              onClick={() => {
                vscode.postMessage({
                  type: 'COMMON',
                  payload: {
                    type: 'get-user-prompts',
                    data: {},
                  },
                });
              }}
            />
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              resetShortcutForm();
              setIsShowShortcutModal(true);
              setIsProjectPrompt(isRemoteEnvironment ? true : false);
            }}
            style={{
              backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
              borderColor: 'var(--vscode-input-border, #72747c)',
              width: '120px',
              height: '28px',
              borderRadius: '4px',
              color: 'var(--vscode-button-secondaryForeground, #72747c)',
              fontSize: '12px',
              padding: '0',
              float: 'right',
              marginLeft: 'auto',
            }}
          >
            新建快捷指令
          </Button>
        </div>

        <List
          className="mt-12"
          style={{ marginBottom: '26px' }}
          dataSource={[
            ...projectShortcuts.map((item) => ({ ...item, _source: 'project' })),
            ...globalShortcuts.map((item) => ({ ...item, _source: 'global' })),
          ]}
          renderItem={(item: any) => (
            <List.Item
              key={item.name}
              style={{
                backgroundColor: 'var(--vscode-editorGroup-border, #72747c)',
                borderRadius: '6px',
                padding: '8px 12px',
                fontSize: '12px',
                lineHeight: '18px',
                marginBottom: '12px',
              }}
            >
              <List.Item.Meta
                title={
                  <>
                    {item.label}
                    <Tag style={{ marginLeft: 5, fontSize: 10, lineHeight: '18px', paddingLeft: 5, paddingRight: 5 }}>
                      {item._source === 'global' ? '全局' : '项目'}快捷指令
                    </Tag>
                  </>
                }
                description={item.description || item.prompt}
              />
              <div className="agent-list-item-options">
                {/* <Switch defaultChecked size="small" /> */}
                <Dropdown
                  placement="bottom"
                  menu={{
                    items: [
                      {
                        key: '1',
                        label: <span>编辑</span>,
                      },
                      {
                        key: '2',
                        label: <span style={{ color: '#FF4838FF' }}>删除</span>,
                      },
                    ],
                    onClick: ({ key }) => {
                      if (key === '1') {
                        handleEditShortcut(item);
                      } else if (key === '2') {
                        handleDeleteShortcut(item);
                      }
                    },
                  }}
                >
                  <div className="icon iconfont icon-gengduo"></div>
                </Dropdown>
              </div>
            </List.Item>
          )}
        />
      </div>

      {/* 新建/编辑快捷指令模态框*/}
      <Modal
        title={shortcutTitle}
        open={isShowShortcutModal}
        onCancel={() => {
          resetShortcutForm();
          setIsShowShortcutModal(false);
        }}
        onOk={handleSaveShortcut}
        destroyOnClose={true}
        okText="保存"
        cancelText="取消"
        style={{ backgroundColor: 'var(--vscode-editor-background, #72747C)' }}
        centered={true}
        width={640}
      >
        <div className="agent-info-top-title fl">
          <div className="agent-info-top-title-text">
            标题 <span style={{ color: 'red' }}>*</span>
          </div>
          <Input
            placeholder="指令列表将会展示该标题，例如'正则表达式专家'"
            className="agent-info-top-title-input"
            value={shortcutFormData.label}
            onChange={(e) => {
              setShortcutFormData((prev) => ({ ...prev, label: e.target.value }));
              // 清除错误提示
              if (shortcutLabelError) {
                setShortcutLabelError('');
              }
            }}
            showCount
            maxLength={20}
            status={shortcutLabelError ? 'error' : ''}
          />
          {shortcutLabelError && <div className="agent-info-top-subtitle color-red">{shortcutLabelError}</div>}
        </div>

        <div className="agent-info-top-title fl">
          <div className="agent-info-top-title-text mt-24">
            名称 <span style={{ color: 'red' }}>*</span>
          </div>
          <Input
            placeholder="将使用该指令名调用快捷指令，例如'RegEx'"
            className="agent-info-top-title-input"
            value={shortcutFormData.name}
            onChange={(e) => {
              setShortcutFormData((prev) => ({ ...prev, name: e.target.value }));
              // 清除错误提示
              if (shortcutNameError) {
                setShortcutNameError('');
              }
            }}
            showCount
            maxLength={20}
            status={shortcutNameError ? 'error' : ''}
          />
          {shortcutNameError && <div className="agent-info-top-subtitle color-red">{shortcutNameError}</div>}
        </div>

        <div className="clearfix"></div>
        <div className="shortcut-info-top">
          <div className="joycoder-setting-box-title mt-24">描述</div>
          <div className="agent-info-top-subtitle">设置快捷指令的描述信息</div>
          <TextArea
            className="joycoder-setting-textarea mt-4"
            placeholder="可描述快捷指令的作用"
            value={shortcutFormData.description}
            onChange={(e) => setShortcutFormData((prev) => ({ ...prev, description: e.target.value }))}
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </div>

        <div className="shortcut-info-top">
          <div className="joycoder-setting-box-title mt-24">提示词</div>
          <div className="agent-info-top-subtitle">设定快捷指令内容</div>
          <TextArea
            className="joycoder-setting-textarea mt-4"
            placeholder="输入预设提示词，提示词可使用快捷指令插入对话中"
            value={shortcutFormData.prompt}
            onChange={(e) => setShortcutFormData((prev) => ({ ...prev, prompt: e.target.value }))}
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </div>

        <div className="shortcut-info-top mt-24">
          <div className="joycoder-setting-box-title">作用范围</div>
          <RadioGroup
            value={isProjectPrompt ? 'project' : 'global'}
            onChange={(e) => {
              setIsProjectPrompt(e.target.value === 'project');
            }}
            disabled={isRemoteEnvironment}
          >
            {!isRemoteEnvironment && <RadioButton value="global">全局可用</RadioButton>}
            <RadioButton value="project">当前项目</RadioButton>
          </RadioGroup>
        </div>
      </Modal>
    </div>
  );
}
