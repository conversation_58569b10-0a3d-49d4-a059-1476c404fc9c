import React from 'react';
import { Button, Form, Input, Modal, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import type { FormInstance } from 'antd/es/form';

interface LocalPopupProps {
  open: boolean;
  title?: string;
  setLocalPopup: (open: boolean) => void;
}

export default function LoaclPopup({ open = false, title, setLocalPopup }: LocalPopupProps) {
  const formRef = React.createRef<FormInstance>();
  const handleOk = async () => {
    // Handle OK action
    try {
      const values = await formRef.current?.validateFields();
      console.log('%c [ values ]-27', 'font-size:13px; background:pink; color:#bf2c9f;', values);
      setLocalPopup(false);
      formRef.current?.resetFields();
    } catch (error) {
      console.error('%c [ error ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  };
  const handleCancel = () => {
    // Handle Cancel action
    setLocalPopup(false);
    formRef.current?.resetFields();
  };
  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };

  const props: UploadProps = {
    name: 'file', //发到后台的文件参数名
    action: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
    multiple: true,
    withCredentials: true,
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: (file) => {
      const isMdOrTxt =
        file.type === 'text/markdown' ||
        file.type === 'text/plain' ||
        file.name.endsWith('.md') ||
        file.name.endsWith('.txt');
      if (!isMdOrTxt) {
        // 只能上传 MD 或 TXT 格式的文件!
        return false; // 阻止上传
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        // console.error('文件必须小于 10MB!');
        return false;
      }
      return true; // 允许上传
    },
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        // message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        // message.error(`${info.file.name} file upload failed.`);
      }
    },
  };

  return (
    <Modal
      title={title || '编辑文档集'}
      open={open}
      okText="确定"
      cancelText="取消"
      onOk={handleOk}
      onCancel={handleCancel}
      // okButtonProps={{ disabled: okButtonDisabled }}
      // cancelButtonProps={{ disabled: cancelButtonDisabled }}
    >
      <Form {...layout} ref={formRef} name="control-ref">
        <Form.Item
          name="knowledgeName"
          validateTrigger={['onBlur']}
          label="文档集名称"
          rules={[{ required: true, message: '请输入文档集名称' }]}
        >
          <Input placeholder="请输入文档集名称" allowClear />
        </Form.Item>
        <Form.Item
          name="files"
          label="本地文件"
          rules={[
            {
              required: true,
              message: '请上传MD或者TXT格式的文件',
              validator: (_, value) => {
                // 检查文件格式
                const invalidFiles = value.filter((file: { type: string; name: string; size: number }) => {
                  const isMdOrTxt =
                    file.type === 'text/markdown' ||
                    file.type === 'text/plain' ||
                    file.name.endsWith('.md') ||
                    file.name.endsWith('.txt');
                  const isLt10M = file.size / 1024 / 1024 < 10;
                  return !isMdOrTxt || !isLt10M;
                });
                if (invalidFiles.length > 0) {
                  return Promise.reject(new Error('支持MD或TXT格式的文件,并且单个文件必须小于 10MB'));
                }
                return Promise.resolve();
              },
            },
          ]}
          valuePropName="fileList"
          className="joycode-popup-local-box"
        >
          <div className="joycoder-setting-box-text l-20 mb-18">
            支持.md/.txt 两种格式，单个文件最大 10 MB，文档集最大50MB，最多添加 100 个文件
          </div>
          <Upload {...props}>
            <Button icon={<UploadOutlined />}>添加文件</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
}
