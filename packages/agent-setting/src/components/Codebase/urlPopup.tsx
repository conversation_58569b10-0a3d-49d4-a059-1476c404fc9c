import React from 'react';
import { Modal, Form, Input, Space, Button } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import layout from 'antd/lib/layout';
import { FormInstance } from 'antd/es/form';

interface URLPopupProps {
  open: boolean;
  title?: string;
  setUrlPopup: (open: boolean) => void;
}

export default function URLPopup({ open = false, title, setUrlPopup }: URLPopupProps) {
  const formRef = React.createRef<FormInstance>();
  const handleOk = async () => {
    // Handle OK action
    try {
      const values = await formRef.current?.validateFields();
      console.log('%c [ values ]-27', 'font-size:13px; background:pink; color:#bf2c9f;', values);
      setUrlPopup(false);
      formRef.current?.resetFields();
    } catch (error) {
      console.error('%c [ error ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  };
  const handleCancel = () => {
    // Handle Cancel action
    setUrlPopup(false);
    formRef.current?.resetFields();
  };
  return (
    <Modal
      title={title || '编辑文档集'}
      open={open}
      okText="确定"
      cancelText="取消"
      onOk={handleOk}
      onCancel={handleCancel}
      // okButtonProps={{ disabled: okButtonDisabled }}
      // cancelButtonProps={{ disabled: cancelButtonDisabled }}
    >
      <Form {...layout} ref={formRef} name="control-ref">
        <Form.Item
          name="knowledgeName"
          validateTrigger={['onBlur']}
          label="文档名称"
          rules={[{ required: true, message: '请输入文档集名称' }]}
        >
          <Input placeholder="请输入文档集名称" allowClear />
        </Form.Item>
        <Form.Item
          name="knowledgeUrl"
          validateTrigger={['onBlur']}
          label="文档URL"
          rules={[{ required: true, message: '请输入文档URL' }]}
        >
          <Input placeholder="请输入文档URL" allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
}
