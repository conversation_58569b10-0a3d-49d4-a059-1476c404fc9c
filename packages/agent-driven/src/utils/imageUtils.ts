import fs from 'fs';
import path from 'path';

interface ImageValidationOptions {
  maxSize?: number; // 最大文件大小(bytes)
  allowedTypes?: string[]; // 允许的图片类型
}

type ImageSignatures = {
  [key: string]: number[][];
};

type ImageMimeTypes = {
  [key: string]: string;
};

export class ImageUtils {
  // 支持的图片类型及其对应的魔数(Magic Numbers)
  private static readonly IMAGE_SIGNATURES: ImageSignatures = {
    jpg: [
      [0xff, 0xd8, 0xff, 0xe0],
      [0xff, 0xd8, 0xff, 0xe1],
      [0xff, 0xd8, 0xff, 0xe8],
    ],
    jpeg: [
      [0xff, 0xd8, 0xff, 0xe0],
      [0xff, 0xd8, 0xff, 0xe1],
      [0xff, 0xd8, 0xff, 0xe8],
    ],
    png: [[0x89, 0x50, 0x4e, 0x47]],
    gif: [[0x47, 0x49, 0x46, 0x38]],
    webp: [[0x52, 0x49, 0x46, 0x46]],
    bmp: [[0x42, 0x4d]],
    tiff: [
      [0x49, 0x49, 0x2a, 0x00], // Little-endian
      [0x4d, 0x4d, 0x00, 0x2a], // Big-endian
    ],
    ico: [[0x00, 0x00, 0x01, 0x00]],
    heic: [[0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63]], // ftyp heic
    avif: [[0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66]], // ftyp avif
  };

  // 默认支持的图片扩展名
  private static readonly DEFAULT_IMAGE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp',
    '.tiff',
    '.ico',
    '.heic',
    '.avif',
  ];

  // MIME类型映射
  private static readonly MIME_TYPES: ImageMimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp',
    '.tiff': 'image/tiff',
    '.ico': 'image/x-icon',
    '.heic': 'image/heic',
    '.avif': 'image/avif',
  };

  /**
   * 检查文件是否为有效的图片
   * @param filePath 文件路径
   * @param options 验证选项
   * @returns Promise<boolean>
   */
  public static async isValidImage(filePath: string, options: ImageValidationOptions = {}): Promise<boolean> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return false;
      }

      // 获取文件扩展名
      const ext = path.extname(filePath).toLowerCase();
      const allowedTypes = options.allowedTypes || this.DEFAULT_IMAGE_EXTENSIONS;

      // 检查扩展名是否在允许列表中
      if (!allowedTypes.includes(ext)) {
        return false;
      }

      // 检查文件大小
      if (options.maxSize) {
        const stats = fs.statSync(filePath);
        if (stats.size > options.maxSize) {
          return false;
        }
      }

      // 读取文件头进行魔数检查
      const buffer = await fs.promises.readFile(filePath, { flag: 'r' });
      return this.verifyImageSignature(buffer, ext.substring(1));
    } catch (error) {
      console.error('Error validating image:', error);
      return false;
    }
  }

  /**
   * 同步检查文件是否为有效的图片
   * @param filePath 文件路径
   * @param options 验证选项
   * @returns boolean
   */
  public static isValidImageSync(filePath: string, options: ImageValidationOptions = {}): boolean {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return false;
      }

      // 获取文件扩展名
      const ext = path.extname(filePath).toLowerCase();
      const allowedTypes = options.allowedTypes || this.DEFAULT_IMAGE_EXTENSIONS;

      // 检查扩展名是否在允许列表中
      if (!allowedTypes.includes(ext)) {
        return false;
      }

      // 检查文件大小
      if (options.maxSize) {
        const stats = fs.statSync(filePath);
        if (stats.size > options.maxSize) {
          return false;
        }
      }

      // 读取文件头进行魔数检查
      const buffer = fs.readFileSync(filePath, { flag: 'r' });
      return this.verifyImageSignature(buffer, ext.substring(1));
    } catch (error) {
      console.error('Error validating image:', error);
      return false;
    }
  }

  /**
   * 获取文件的MIME类型
   * @param filePath 文件路径
   * @returns string | null
   */
  public static getImageMimeType(filePath: string): string | null {
    const ext = path.extname(filePath).toLowerCase();
    return this.MIME_TYPES[ext] || null;
  }

  /**
   * 验证文件头魔数
   * @param buffer 文件buffer
   * @param type 图片类型
   * @returns boolean
   */
  private static verifyImageSignature(buffer: Buffer, type: string): boolean {
    const signatures = this.IMAGE_SIGNATURES[type];
    if (!signatures) {
      return false;
    }

    return signatures.some((signature: number[]) => {
      return signature.every((byte: number, index: number) => buffer[index] === byte);
    });
  }

  /**
   * 获取支持的图片扩展名列表
   * @returns string[]
   */
  public static getSupportedExtensions(): string[] {
    return [...this.DEFAULT_IMAGE_EXTENSIONS];
  }

  /**
   * 动态加载Sharp模块
   * @returns Promise<any | null> Sharp实例或null
   */
  private static async loadSharp(): Promise<any | null> {
    try {
      // 尝试动态导入sharp模块
      const sharp = require('sharp');
      return sharp;
    } catch (error) {
      // Sharp模块不可用或加载失败
      console.warn('Sharp module is not available:', error.message);
      return null;
    }
  }

  /**
   * 检查文件大小是否在限制范围内
   * @param filePath 文件路径
   * @param maxSize 最大文件大小(bytes)
   * @returns boolean
   */
  public static checkImageSize(filePath: string, maxSize: number): boolean {
    try {
      const stats = fs.statSync(filePath);
      return stats.size <= maxSize;
    } catch (error) {
      console.error('Error checking image size:', error);
      return false;
    }
  }

  /**
   * 根据文件路径读取图片并转换为base64
   * - 500K以内的图片直接转换
   * - 500K-2M的图片进行压缩至500K以内
   * - 超过2M的图片返回空字符串
   * @param filePath 图片文件路径
   * @returns Promise<string> base64字符串或空字符串
   */
  public static async convertImageToBase64(filePath: string): Promise<string> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        console.error('Image file does not exist:', filePath);
        return `(Image file does not exist: ${filePath})`;
      }

      // 检查是否为有效图片
      const isValid = await this.isValidImage(filePath);
      if (!isValid) {
        console.error('Invalid image file:', filePath);
        return `(Invalid image file: ${filePath})`;
      }

      // 获取文件大小
      const stats = fs.statSync(filePath);
      const fileSizeInBytes = stats.size;
      const fileSizeInKB = fileSizeInBytes / 1024;
      const fileSizeInMB = fileSizeInKB / 1024;

      // 超过2M的图片直接返回空字符串
      if (fileSizeInMB > 2) {
        console.warn('Image file too large (>2MB):', filePath, `${fileSizeInMB.toFixed(2)}MB`);
        return '(The file is too large to be read into the context)';
      }

      // 500K以内的图片直接转换
      if (fileSizeInKB <= 500) {
        return this.fileToBase64(filePath);
      }

      // 500K-2M的图片需要压缩
      return await this.compressAndConvertToBase64(filePath, 500 * 1024); // 500KB
    } catch (error) {
      console.error('Error converting image to base64:', error);
      return '(Error converting image to base64)';
    }
  }

  /**
   * 直接将文件转换为base64
   * @param filePath 文件路径
   * @returns string base64字符串
   */
  private static fileToBase64(filePath: string): string {
    try {
      const buffer = fs.readFileSync(filePath);
      const mimeType = this.getImageMimeType(filePath) || 'image/jpeg';
      return `data:${mimeType};base64,${buffer.toString('base64')}`;
    } catch (error) {
      console.error('Error reading file to base64:', error);
      return '';
    }
  }

  /**
   * 压缩图片并转换为base64
   * 使用简单的质量压缩策略，通过调整JPEG质量来减小文件大小
   * @param filePath 图片文件路径
   * @param targetSize 目标大小(bytes)
   * @returns Promise<string> base64字符串
   */
  private static async compressAndConvertToBase64(filePath: string, targetSize: number): Promise<string> {
    try {
      // 尝试使用sharp进行压缩（如果可用）
      const sharp = await this.loadSharp();
      if (sharp) {
        return await this.compressWithSharp(filePath, targetSize, sharp);
      } else {
        // sharp不可用，使用备用方案
        console.warn('Sharp not available, using fallback compression method');
        return await this.compressWithFallback(filePath, targetSize);
      }
    } catch (error) {
      console.error('Error compressing image:', error);
      // 如果压缩失败，尝试直接转换
      return this.fileToBase64(filePath);
    }
  }

  /**
   * 使用Sharp进行图片压缩
   * @param filePath 图片文件路径
   * @param targetSize 目标大小(bytes)
   * @param sharp Sharp实例
   * @returns Promise<string> base64字符串
   */
  private static async compressWithSharp(filePath: string, targetSize: number, sharp: any): Promise<string> {
    let quality = 80;
    let attempts = 0;
    const maxAttempts = 10;
    const ext = path.extname(filePath).toLowerCase();
    const mimeType = this.getImageMimeType(filePath) || 'image/jpeg';

    while (attempts < maxAttempts) {
      let buffer: Buffer;

      if (ext === '.png') {
        // PNG压缩
        buffer = await sharp(filePath).png({ quality, compressionLevel: 9 }).toBuffer();
      } else if (ext === '.webp') {
        // WebP压缩
        buffer = await sharp(filePath).webp({ quality }).toBuffer();
      } else {
        // JPEG压缩
        buffer = await sharp(filePath).jpeg({ quality }).toBuffer();
      }

      // 检查压缩后的大小
      if (buffer.length <= targetSize) {
        return `data:${mimeType};base64,${buffer.toString('base64')}`;
      }

      // 降低质量继续尝试
      quality -= 10;
      if (quality < 10) {
        // 如果质量太低，尝试缩小尺寸
        const metadata = await sharp(filePath).metadata();
        const scale = Math.sqrt(targetSize / buffer.length);
        const newWidth = Math.floor((metadata.width || 800) * scale);
        const newHeight = Math.floor((metadata.height || 600) * scale);

        buffer = await sharp(filePath).resize(newWidth, newHeight).jpeg({ quality: 70 }).toBuffer();

        if (buffer.length <= targetSize) {
          return `data:${mimeType};base64,${buffer.toString('base64')}`;
        }
        break;
      }

      attempts++;
    }

    // 如果无法压缩到目标大小，返回最后一次的结果
    const finalBuffer = await sharp(filePath).jpeg({ quality: 10 }).toBuffer();
    return `data:${mimeType};base64,${finalBuffer.toString('base64')}`;
  }

  /**
   * 备用压缩方案（当Sharp不可用时）
   * 简单地通过读取原始文件并尝试基本的base64转换
   * @param filePath 图片文件路径
   * @param targetSize 目标大小(bytes)
   * @returns Promise<string> base64字符串
   */
  private static async compressWithFallback(filePath: string, targetSize: number): Promise<string> {
    // 备用方案：直接转换，但给出警告
    console.warn('Using fallback compression method - image may not be compressed to target size');
    const originalBase64 = this.fileToBase64(filePath);

    // 计算base64的实际大小
    const base64Data = originalBase64.split(',')[1];
    const actualSize = (base64Data.length * 3) / 4;

    if (actualSize <= targetSize) {
      return originalBase64;
    }

    // 如果仍然太大，返回空字符串
    console.warn('Unable to compress image to target size without proper compression library');
    return '(Unable to compress image to target size without proper compression library)';
  }

  /**
   * 同步版本：根据文件路径读取图片并转换为base64
   * 注意：此方法不支持压缩，超过500K的图片将返回空字符串
   * @param filePath 图片文件路径
   * @returns string base64字符串或空字符串
   */
  public static convertImageToBase64Sync(filePath: string): string {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        console.error('Image file does not exist:', filePath);
        return '';
      }

      // 检查是否为有效图片
      const isValid = this.isValidImageSync(filePath);
      if (!isValid) {
        console.error('Invalid image file:', filePath);
        return '';
      }

      // 获取文件大小
      const stats = fs.statSync(filePath);
      const fileSizeInKB = stats.size / 1024;
      const fileSizeInMB = fileSizeInKB / 1024;

      // 超过2M的图片直接返回空字符串
      if (fileSizeInMB > 2) {
        console.warn('Image file too large (>2MB):', filePath, `${fileSizeInMB.toFixed(2)}MB`);
        return '';
      }

      // 超过500K的图片在同步方法中不支持压缩，返回空字符串
      if (fileSizeInKB > 500) {
        console.warn('Image file too large for sync conversion (>500KB), use async method instead:', filePath);
        return '';
      }

      // 500K以内的图片直接转换
      return this.fileToBase64(filePath);
    } catch (error) {
      console.error('Error converting image to base64 (sync):', error);
      return '';
    }
  }
  /**
   * 综合转换图片为base64的函数
   * 根据文件大小自动选择处理策略：
   * - 大于500K：调用压缩转换
   * - 小于等于500K：直接转换
   * @param filePath 图片文件路径
   * @returns Promise<string> base64字符串或空字符串
   */
  public static async convertToBase64(filePath: string): Promise<string> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        console.error('Image file does not exist:', filePath);
        return '';
      }

      // 检查是否为有效图片
      const isValid = await this.isValidImage(filePath);
      if (!isValid) {
        console.error('Invalid image file:', filePath);
        return '';
      }

      // 获取文件大小
      const stats = fs.statSync(filePath);
      const fileSizeInKB = stats.size / 1024;
      const fileSizeInMB = fileSizeInKB / 1024;

      // 超过2M的图片直接返回空字符串
      if (fileSizeInMB > 2) {
        console.warn('Image file too large (>2MB):', filePath, `${fileSizeInMB.toFixed(2)}MB`);
        return '';
      }

      // 根据文件大小选择处理策略
      if (fileSizeInKB <= 500) {
        // 500K以内直接转换
        return this.fileToBase64(filePath);
      } else {
        // 大于500K进行压缩转换
        return await this.compressAndConvertToBase64(filePath, 500 * 1024);
      }
    } catch (error) {
      console.error('Error converting image to base64:', error);
      return '';
    }
  }
}
