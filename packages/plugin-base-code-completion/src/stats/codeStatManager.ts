import * as vscode from 'vscode';
import { v5 as uuidv5 } from 'uuid';
import { getJdhLoginInfo, hasJdhLoginCookie, pluginVersion } from '@joycoder/shared';
import getDocumentLanguage from '../utils/getDocumentLanguage';
import { codeStatService } from './codeStatService';
import AdoptResultCache from './adoptResultCache';
import fileChangeMonitor, { FileChangeInfo } from './fileChangeMonitor';
import { FileSystemHelper } from '@joycoder/agent-driven/src/utils/FileSystemHelper';
import { cloneDeep } from 'lodash-es';

// 最近一次的文档快照
let originText = '';

export async function hookCodeStatistics(context: vscode.ExtensionContext) {
  // 注册事件监听，将文档变化数据采集上报到GO程序
  context.subscriptions.push(
    vscode.workspace.onDidChangeTextDocument((e) => registerCodeStatisticsListenForTextEditor(e))
  );
  // 启动服务GO服务
  await codeStatService.checkAndStartService();

  // 同步登录态
  codeStatService.updatePtk('updatePtk:' + (getJdhLoginInfo()?.ptKey || ''));

  // 注册立即上报的命令
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.ai.log.reportImmediate', () => {
      codeStatService.sendReportImmediate();
    })
  );
  // 维护文件第一次打开的快照：originText，用于convertTextDocumentChangeEvent中计算位置
  originText = vscode.window.activeTextEditor?.document.getText() || '';
  context.subscriptions.push(
    vscode.window.onDidChangeActiveTextEditor((editor: vscode.TextEditor) => {
      // 文件切换后的立即上报，由GO的二进制来切换
      // codeStatService.sendReportImmediate();
      if (editor) {
        originText = editor.document.getText();
      }
    })
  );

  //文件移动、复制粘贴监听
  try {
    fileChangeMonitor(context, (fileChangeInfo: FileChangeInfo) => {
      console.log('fileChangeMonitor', fileChangeInfo);
      codeStatService.sendCodeChangeToStat(fileChangeInfo);
    });
  } catch (error) {
    console.error('fileChangeMonitor error', error);
  }
}

export async function registerCodeStatisticsListenForTextEditor(e: vscode.TextDocumentChangeEvent) {
  const isLogin = !!hasJdhLoginCookie();
  const activeTextEditor = vscode.window.activeTextEditor;
  if (!isLogin || !activeTextEditor) {
    return;
  }

  // 针对特定类型的语言进行统计
  const lang = getDocumentLanguage(activeTextEditor);
  if (!lang) {
    return;
  }

  if (e.document != null && e.document.uri.scheme === 'file') {
    // 生成JSON文档
    if (e.contentChanges.length > 0) {
      const docChangeList: any[] = convertTextDocumentChangeEvent(e);
      // 对数据进行统计
      docChangeList.forEach((d) => {
        codeStatService.sendCodeChangeToStat(d);
      });
    }
  }
}
/**
 * 将事件中的参数信息转换为统计程序可以识别的格式
 *
 * @param e 文档变更事件
 * @param adoptType 补全类型
 * @returns
 */
function convertTextDocumentChangeEvent(e: vscode.TextDocumentChangeEvent) {
  console.log('%c [ e: vscode.TextDocumentChangeEvent ]-84', 'font-size:13px; background:pink; color:#bf2c9f;', e);
  const doc = e.document;
  // 处理多个contentChange
  const loginInfo = getJdhLoginInfo();
  const docChangeBasicInfo = {
    text: doc.getText(),
    filename: doc.uri.fsPath,
    uuid: uuidv5(doc.uri.fsPath, uuidv5.URL),
    // 文档语言
    lang: doc.languageId,
    // 当前的毫秒数
    timeMills: new Date().getTime(),
    // 变更的位置--在下面插入计算
    // offset: contentChange.rangeOffset,
    userName: loginInfo?.erp || loginInfo?.userName,
    ideType: 'vscode',
    projectPath: getCurrentProjectPath(), //当前变动文档对应的项目全路径
    pluginVersion: pluginVersion,
    status: 1,
  };
  // 从 contentChanges 中找到 text 长度最长的元素
  const longestTextChange = e.contentChanges.reduce((longest, current) => {
    return current.text.length > longest.text.length ? current : longest;
  });

  const {
    type: adoptType,
    text: adoptText,
    codeSource,
    model,
    conversationId,
  } = AdoptResultCache.getAndClearAdopt(longestTextChange.text);

  // const contentChange = e.contentChanges[e.contentChanges.length - 1];
  const docChangeList: any[] = [];
  // 文本信息
  let modifiedOffset = 0;
  let textAfterCurrentEdit = originText;
  for (let i = e.contentChanges.length - 1; i >= 0; i--) {
    const contentChange = e.contentChanges[i];
    const realOffset = contentChange.rangeOffset + modifiedOffset;
    console.log('%c [ EEEEEE realOffset ]-123', 'font-size:13px; background:pink; color:#bf2c9f;', realOffset);
    docChangeBasicInfo['offset'] = realOffset;
    if (contentChange.rangeLength) {
      // 先发送删除操作
      const docDelObj = cloneDeep(docChangeBasicInfo);
      docDelObj['newStr'] = '';
      docDelObj['newLen'] = 0;
      docDelObj['oldStr'] = textAfterCurrentEdit.substring(realOffset, realOffset + contentChange.rangeLength);
      docDelObj['oldLen'] = contentChange.rangeLength;
      textAfterCurrentEdit =
        textAfterCurrentEdit.substring(0, realOffset) +
        textAfterCurrentEdit.substring(realOffset + contentChange.rangeLength); //模拟编辑文件操作
      console.log(
        '%c [ EEEEEE textAfterCurrentEdit ]-delete',
        'font-size:13px; background:pink; color:#bf2c9f;',
        textAfterCurrentEdit
      );

      docDelObj['text'] = textAfterCurrentEdit;
      docChangeList.push(docDelObj);
    }
    if (contentChange.text) {
      // 再发送新增操作
      const docChangeObj = cloneDeep(docChangeBasicInfo);
      docChangeObj['newStr'] = contentChange.text;
      docChangeObj['newLen'] = contentChange.text.length;
      docChangeObj['oldStr'] = '';
      docChangeObj['oldLen'] = 0;
      textAfterCurrentEdit =
        textAfterCurrentEdit.substring(0, realOffset) + contentChange.text + textAfterCurrentEdit.substring(realOffset); //模拟编辑文件操作
      docChangeObj['text'] = textAfterCurrentEdit;
      console.log(
        '%c [ EEEEEE textAfterCurrentEdit ]-add',
        'font-size:13px; background:pink; color:#bf2c9f;',
        textAfterCurrentEdit
      );

      if (adoptType === AdoptResultCache.TYPE.REMOTE_JOY_CODER) {
        docChangeObj['adoptText'] = adoptText;
        docChangeObj['adoptType'] = adoptType;
        docChangeObj['codeSource'] = codeSource;
        docChangeObj['model'] = model;
        docChangeObj['itemId'] = conversationId;
      } else if (adoptType === AdoptResultCache.TYPE.LOCAL_SYSTEM) {
        // TODO: 目前实际没有上报本地系统预测的结果（exp: VSCode中通过ts类型推断的联想），统计侧反馈不care
        docChangeObj['adoptText'] = adoptText;
        docChangeObj['adoptType'] = adoptType;
        docChangeObj['codeSource'] = codeSource;
      }
      docChangeList.push(docChangeObj);
    }
    modifiedOffset += -contentChange.rangeLength + contentChange.text.length; //累积修改的行数
  }
  originText = doc.getText();
  console.log('EEEEEEE---> docChangeEvent', new Date().getTime());
  console.log('%c [EEEEEEE--->  adoptType ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', adoptType);

  console.log('%c [EEEEEEE--->  adoptText ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', adoptText);
  console.log('%c [EEEEEEE--->  e ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', e);
  console.log('%c [EEEEEEE--->  docChangeList ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', docChangeList);

  return docChangeList;
}

/**
 * 获取当前文件所属项目的根目录路径
 * @returns {string} 当前文件所属项目的根目录路径或提示信息
 */
export function getCurrentProjectPath() {
  // 获取当前活动的文本编辑器
  try {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      // 获取当前文档的 URI
      const documentUri = editor.document.uri;
      // 获取当前文档所属的工作区文件夹
      const workspaceFolder = vscode.workspace.getWorkspaceFolder(documentUri);
      if (workspaceFolder) {
        // 获取工作区文件夹的路径
        return workspaceFolder.uri.fsPath;
      } else {
        return '';
      }
    } else {
      return '';
    }
  } catch (error) {
    console.log('🚀 ~ getCurrentProjectPath ~ error:', error);
    return '';
  }
}
