import fs from 'fs/promises';
import os from 'os';
import * as path from 'path';
import { JoyCoderProvider } from '../../core/webview/JoycoderProvider';
import to from 'await-to-js';
import axios from 'axios';

class DocTracker {
  private providerRef: WeakRef<JoyCoderProvider>;
  private docPath: string = '';

  private readonly BASE_URL = 'http://agentflow.jd.com/api/v1/retrieval';
  private readonly baseHeaders = {
    Authorization: 'Bearer ragflow-I1OTI2MmMwNjYyYzExZjBiOGFlMDI0Mm',
  };
  private readonly headers = {
    ...this.baseHeaders,
    'content-Type': 'application/json',
  };

  constructor(provider: JoyCoderProvider) {
    this.providerRef = new WeakRef(provider);
    const dataStesPath = path.join(os.homedir(), '.joycode', 'knowledge-base.json');
    this.docPath = dataStesPath;
  }

  private async getDocList() {
    try {
      const docs = await fs.readFile(this.docPath, 'utf-8');
      if (docs) {
        return JSON.parse(docs);
      }
    } catch (error) {}
    return [];
  }

  async updateDocList() {
    const docList = await this.getDocList();
    this.providerRef.deref()?.postMessageToWebview({
      type: 'docListUpdated',
      docList,
    });
  }

  async getDocContent(props: { name: string; question: string } | undefined) {
    const { name, question } = props || {};
    const dataset_ids = (await this.getDocList())
      ?.filter((item: { name: string | undefined }) => item.name === name)
      ?.map((item: any) => item.id);
    const [err, response]: [any, any] = await to(
      axios.post(
        `${this.BASE_URL}`,
        {
          dataset_ids,
          question,
          rerank_id: 'qwen.qwen-8b-rank@JoyCode-AI',
          top_k: 10,
          similarity_threshold: 0.9,
        },
        { headers: this.headers }
      )
    );
    if (err) {
      return '';
    }
    const resData = response.data?.data?.chunks?.map((item: { content: string }) => item.content).join('\n');
    return resData;
  }
}

export default DocTracker;
