import { type ClientOptions } from 'openai';
import { type BaseChatModelParams } from '@langchain/core/language_models/chat_models';
import { ChatOpenAI } from '@langchain/openai';
import { LegacyOpenAIInput, OpenAIChatInput } from '@langchain/openai';
import { forceJdhLogin, getJdhLoginInfo, hasJdhLoginCookie } from '@joycoder/shared';
import { type BaseMessage } from '@langchain/core/messages';
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager';
import { type ChatResult } from '@langchain/core/outputs';
import to from 'await-to-js';
import { getExtParams } from '../shared/utils';
import { BASE_URL } from '../shared/constants';
import { getChatModelAndConfig } from '../../model';

export type IChatModelOptions = Partial<OpenAIChatInput> &
  BaseChatModelParams & {
    configuration?: ClientOptions & LegacyOpenAIInput;
  };

/**
 *使用JoyCoder私有服务的ChatModel类
 * @link https://js.langchain.com/docs/modules/model_io/chat/quick_start
 * @example
 * ```typescript
 *const model = new ChatJoyCoderAI({
 *  temperature: 0,
 *  // streaming: false, // 是否开启流式，默认开启
 *});
 *
 * const messages = [new HumanMessage('您好')];
 *
 * // 非流式
 * // const res = await model.invoke(messages);
 * // Logger.log('非流式', res);
 *
 * // 流式
 * const response = await model.invoke(messages, {
 *   callbacks: [
 *     {
 *       handleLLMNewToken(token: string) {
 *         Logger.log('流式', { token });
 *       },
 *     },
 *   ],
 * });
 *
 * Logger.log('ChatJoyCoderAI:', response);
 * ```
 */
export class ChatJoyCoderAI extends ChatOpenAI {
  _llmType(): string {
    return 'joycoderai';
  }

  modelKwargs: OpenAIChatInput['modelKwargs'];

  constructor(fields?: IChatModelOptions) {
    const newFields = fields ? { ...fields } : {};

    // 绕过ChatOpenAI内部校验
    newFields.openAIApiKey = 'not-empty';

    // 默认为流式
    newFields.streaming = newFields.streaming ?? true;

    // 使用设置的模型
    const modelConfig = getChatModelAndConfig(newFields.modelName);
    newFields.modelName = modelConfig.chatApiModel;

    // 要透传给接口的扩展参数放在modelKwargs中
    newFields.modelKwargs = {
      ...newFields.modelKwargs,
      ...getJdhLoginInfo(),
      sourceType: 'joyCoderFe',
      extParams: getExtParams(),
    };

    if (modelConfig.chatApiUrl) newFields.modelKwargs.extParams.apiUrl = modelConfig.chatApiUrl;

    // 设置服务URL
    newFields.configuration = {
      ...newFields.configuration,
      baseURL: BASE_URL,
    };

    // 禁用默认的重试次数，默认6次
    newFields.maxRetries = newFields.maxRetries ?? 0;

    super(newFields);

    this.modelKwargs = newFields.modelKwargs;
  }

  /** @ignore */
  async _generate(
    messages: BaseMessage[],
    options: this['ParsedCallOptions'],
    runManager?: CallbackManagerForLLMRun
  ): Promise<ChatResult> {
    // 校验登录态，引导登录，更新登录参数
    const isLogin = !!hasJdhLoginCookie();
    if (!isLogin) {
      await to(forceJdhLogin());
      // @ts-ignore
      this.modelKwargs.extParams.jdhLoginParams = { ...getJdhLoginInfo(), sourceType: 'joyCoderFe' };
    }

    return await super._generate(messages, options, runManager);
  }
}
